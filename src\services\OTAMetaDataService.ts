/* eslint-disable no-await-in-loop */
import { logger } from '../helpers';
import {
  BASE_S3_OTA_FILE_PATH,
  Edge,
  FilterInput,
  OtaGatewayJobInfo,
  OtaMetadataResponse,
  Targets,
  TargetVersionSearchParams,
  VersionAvailabilityInfo,
} from '../models';
import { EdgeModel } from '../schemas';
import { Connectivity, TargetDirectories, TargetFirmwareNames } from '../enum';
import { S3Service } from './S3Service';
import { IotService } from './IotService';

export class OTAMetaDataService {
  private readonly s3Service: S3Service;

  private readonly iotService: IotService;

  constructor() {
    this.s3Service = new S3Service();
    this.iotService = IotService.getInstance();
  }

  /**
   * Get all edges with pagination from alleadges collection
   */
  public async getOTAMetaData(filters: FilterInput, page: number, limit: number): Promise<OtaMetadataResponse> {
    try {
      // Validate pagination parameters
      if (!page || page < 1) {
        throw new Error(`Invalid page parameter provided: ${page}`);
      }

      if (!limit || limit < 1) {
        throw new Error(`Invalid limit parameter provided: ${limit}`);
      }

      const sortField = 'updatedAt';
      const sortDirection = 1; // setting 1 for 'asc'
      const conditions: any[] = [];

      // FIXED: Search term filter with proper regex escaping and optimized trim usage
      if (filters?.searchTerm?.length) {
        const processedSearchTerms = filters.searchTerm
          .map((term) => term?.trim())
          .filter((term) => term && term.length > 0);

        if (processedSearchTerms.length > 0) {
          const searchConditions = processedSearchTerms.flatMap((term) => {
            const escapedTerm = this.escapeRegexSpecialChars(term);
            return [
              { siteName: { $regex: escapedTerm, $options: 'i' } },
              { edgeId: { $regex: escapedTerm, $options: 'i' } },
            ];
          });

          conditions.push({ $or: searchConditions });
        }
      }

      // Connectivity filter
      if (filters?.connectivity?.length) {
        const connectivityValues = filters.connectivity.map((connection) => connection === Connectivity.Online);
        conditions.push({ isOnline: { $in: connectivityValues } });
      }

      // Target name filter
      if (filters?.targetName?.length) {
        conditions.push({ 'currentJob.targets.targetName': { $in: filters.targetName } });
      }

      // Target version filter
      if (filters?.targetVersion?.length) {
        conditions.push({ 'allTargets.version': { $in: filters.targetVersion } });
      }

      // Job status filter
      if (filters?.jobStatus?.length) {
        conditions.push({ 'currentJob.jobStatus': { $in: filters.jobStatus } });
      }

      // Combine all conditions with AND
      const query = conditions.length > 0 ? { $and: conditions } : {};

      // Get total count
      const totalRecords = await EdgeModel.countDocuments(query);

      // Preparing pagination
      const skip = (page - 1) * limit;
      const edges: Edge[] = (await EdgeModel.find(query)
        .sort({ [sortField]: sortDirection })
        .skip(skip)
        .limit(limit)
        .lean()
        .exec()) as Edge[];

      const transformedEdges = this.transformMongoDataToOtaResponse(edges);
      const totalPages = Math.ceil(totalRecords / limit);

      return {
        data: transformedEdges,
        totalRecords,
        currentPage: page,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };
    } catch (error) {
      logger.error('Error getting OTA metadata:', { error });
      throw error;
    }
  }

  /**
   * Escapes regex special characters to ensure literal string matching
   * Handles: . * + ? ^ $ { } ( ) | [ ] \
   */
  private escapeRegexSpecialChars(term: string): string {
    const originalTerm = term;
    const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // Log when escaping occurs (useful for debugging)
    if (originalTerm !== escapedTerm) {
      logger.debug('Escaped regex special characters in search term', {
        original: originalTerm,
        escaped: escapedTerm,
      });
    }

    return escapedTerm;
  }

  private transformMongoDataToOtaResponse(edges: Edge[]): OtaGatewayJobInfo[] {
    return edges.map((edge) => {
      return {
        thingId: edge.edgeId,
        siteName: edge.siteName || '',
        connectivity: edge.isOnline ? Connectivity.Online : Connectivity.Offline,
        allTargets:
          edge.allTargets.map((target) => ({ targetName: target.targetName, currentVersion: target.version })) || [],
        jobId: edge.currentJob?.jobId || null,
        jobStatus: edge.currentJob?.jobStatus || null,
        currentJobDetails: {
          completedAt: edge.currentJob?.completedAt || null,
          targets:
            edge.currentJob?.targets.map((target: Targets) => ({
              targetName: target.targetName,
              updateVersion: target.version,
            })) || [],
        },
      } as OtaGatewayJobInfo;
    });
  }

  public async getTargetVersionsFromS3({
    targetDirectory,
    versionFilter,
  }: {
    targetDirectory: string;
    versionFilter?: string;
  }): Promise<{ versionDetails: VersionAvailabilityInfo[] }> {
    const isESP32Directory = (directory: string): boolean => {
      return directory === TargetDirectories.ESP32_firmware || directory === TargetDirectories.ESP32_filesystem;
    };

    if (isESP32Directory(targetDirectory)) {
      const directoriesToSearch = [TargetDirectories.ESP32_firmware, TargetDirectories.ESP32_filesystem];
      return this.getESP32CombinedVersions(directoriesToSearch, versionFilter);
    }

    return this.getSingleDirectoryVersions(targetDirectory, versionFilter);
  }

  private async getESP32CombinedVersions(
    directories: string[],
    versionFilter?: string,
  ): Promise<{ versionDetails: VersionAvailabilityInfo[] }> {
    const versionFileMap = new Map<string, Set<string>>();

    // Collect versions from all ESP32 directories
    for (const directory of directories) {
      const versions = await this.collectVersionsFromDirectory(directory, versionFilter);
      this.mergeVersionsIntoMap(versions, directory, versionFileMap);
    }

    const sortedVersions = this.getSortedVersions(versionFileMap);
    const versionDetails = await this.getESP32VersionDetails(sortedVersions, versionFileMap);

    return { versionDetails };
  }

  private async collectVersionsFromDirectory(directory: string, versionFilter?: string): Promise<string[]> {
    const filePath = `${BASE_S3_OTA_FILE_PATH}${directory}/`;
    const params = {
      Prefix: filePath,
      Delimiter: '/',
    };

    const { prefixes } = await this.s3Service.listFilesInS3(params, versionFilter);

    return prefixes.map((prefix) => prefix.replace(filePath, '').replace(/\/$/, '')).filter(Boolean);
  }

  private mergeVersionsIntoMap(versions: string[], directory: string, versionFileMap: Map<string, Set<string>>): void {
    for (const version of versions) {
      if (!versionFileMap.has(version)) {
        versionFileMap.set(version, new Set());
      }
      const versionSet = versionFileMap.get(version);
      if (versionSet) {
        versionSet.add(directory);
      }
    }
  }

  private getSortedVersions(versionFileMap: Map<string, Set<string>>): string[] {
    return Array.from(versionFileMap.keys()).sort((a, b) => a.localeCompare(b, undefined, { numeric: true }));
  }

  private async getESP32VersionDetails(
    sortedVersions: string[],
    versionFileMap: Map<string, Set<string>>,
  ): Promise<VersionAvailabilityInfo[]> {
    const versionDetailsPromises = sortedVersions.map(async (version: string) => {
      const availableDirectories = versionFileMap.get(version);
      if (!availableDirectories) {
        return { version, availableFiles: [] };
      }

      const availableFiles = await this.getFilesForESP32Version(version, Array.from(availableDirectories));
      return { version, availableFiles };
    });

    return Promise.all(versionDetailsPromises);
  }

  private async getFilesForESP32Version(version: string, directories: string[]): Promise<string[]> {
    const allFiles: string[] = [];

    for (const directory of directories) {
      try {
        const files = await this.getFilesFromVersionDirectory(directory, version);
        const mappedFiles = this.mapESP32Files(files, directory);
        allFiles.push(...mappedFiles);
      } catch (error) {
        logger.error(`Error listing files for ESP32 version ${version} in directory ${directory}:`, { error });
      }
    }

    return [...new Set(allFiles)]; // Remove duplicates
  }

  private async getFilesFromVersionDirectory(directory: string, version: string): Promise<string[]> {
    const versionPath = `${BASE_S3_OTA_FILE_PATH}${directory}/${version}/`;
    const listParams = { Prefix: versionPath };
    const filesResponse = await this.s3Service.listObjectsInDirectory(listParams);

    return filesResponse
      .filter((item: any) => item.Key && !item.Key.endsWith('/'))
      .map((item: any) => item.Key?.split('/').pop()?.split('.')[0] || '')
      .filter(Boolean);
  }

  private mapESP32Files(files: string[], directory: string): string[] {
    return files.map(() => {
      if (directory === TargetDirectories.ESP32_firmware) {
        return TargetDirectories.ESP32_firmware;
      }
      if (directory === TargetDirectories.ESP32_filesystem) {
        return TargetDirectories.ESP32_filesystem;
      }
      return directory;
    });
  }

  private async getSingleDirectoryVersions(
    targetDirectory: string,
    versionFilter?: string,
  ): Promise<{ versionDetails: VersionAvailabilityInfo[] }> {
    const versions = await this.collectVersionsFromDirectory(targetDirectory, versionFilter);
    const sortedVersions = versions.sort((a, b) => a.localeCompare(b, undefined, { numeric: true }));
    const versionDetails = await this.getSingleDirectoryVersionDetails(targetDirectory, sortedVersions);

    return { versionDetails };
  }

  private async getSingleDirectoryVersionDetails(
    targetDirectory: string,
    versions: string[],
  ): Promise<VersionAvailabilityInfo[]> {
    const versionDetailsPromises = versions.map(async (version: string) => {
      try {
        const files = await this.getFilesFromVersionDirectory(targetDirectory, version);
        return { version, availableFiles: files };
      } catch (error) {
        logger.error(`Error listing files for version ${version}:`, { error });
        return { version, availableFiles: [] };
      }
    });

    return Promise.all(versionDetailsPromises);
  }

  public async getCurrenTargetVersions({ search = '', page = 1, limit = 10 }: TargetVersionSearchParams) {
    try {
      const targetNames = Object.keys(TargetFirmwareNames);

      const pipeline = [
        {
          $match: {
            isDeleted: false,
            allTargets: { $exists: true, $ne: [] },
          },
        },
        { $unwind: '$allTargets' },
        {
          $match: {
            'allTargets.targetName': { $in: targetNames },
            'allTargets.version': { $ne: '', $exists: true },
            ...(search &&
              search.trim() && {
                'allTargets.version': { $regex: search.trim(), $options: 'i' },
              }),
          },
        },
        {
          $group: {
            _id: '$allTargets.version',
          },
        },
        { $replaceRoot: { newRoot: { version: '$_id' } } },
        { $sort: { version: 1 } },
      ];

      const [results, totalResults] = await Promise.all([
        EdgeModel.aggregate([...(pipeline as any[]), { $skip: (page - 1) * limit }, { $limit: limit }]),
        EdgeModel.aggregate([...(pipeline as any[]), { $count: 'total' }]),
      ]);

      const total = totalResults[0]?.total || 0;

      return {
        total,
        page,
        pageSize: limit,
        totalPages: Math.ceil(total / limit),
        versions: results.map((r: { targetName: string; version: string }) => r.version),
      };
    } catch (error) {
      logger.error('Error getting target versions:', { error });
      throw error;
    }
  }
}
