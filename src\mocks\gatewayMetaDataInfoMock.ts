import { Connectivity, JobStatus } from '../enum';

export const gatewayMetaDataInfoMock = [
  {
    thingId: 'FCTC26061322010',
    siteName: 'Sample site name',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.0',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.2',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.2',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.0',
      },
      {
        targetName: 'claim',
        currentVersion: '1.1',
      },
    ],
    jobId: '89MIS8YBQ4',
    jobStatus: JobStatus.TimedOut,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [
        {
          targetName: 'AmazonRootCA1',
          updateVersion: '1.2',
        },
      ],
    },
  },
  {
    thingId: '831251972602112',
    siteName: 'Sample site on qa.',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.0',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.2',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.2',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.0',
      },
      {
        targetName: 'claim',
        currentVersion: '1.1',
      },
    ],
    jobId: null,
    jobStatus: null,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'thing_id_1',
    siteName: 'Sample site name',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.0',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.2',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.2',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.0',
      },
      {
        targetName: 'claim',
        currentVersion: '1.1',
      },
    ],
    jobId: '89MIS8YBQ4',
    jobStatus: JobStatus.InProgress,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [
        {
          targetName: 'AmazonRootCA1',
          updateVersion: '1.1',
        },
      ],
    },
  },
  {
    thingId: 'FCTC26061322011',
    siteName: 'Manufacturing Plant A',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.1',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.3',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.3',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.1',
      },
      {
        targetName: 'claim',
        currentVersion: '1.2',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.1',
      },
    ],
    jobId: 'JOB001ABC',
    jobStatus: JobStatus.Success,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322012',
    siteName: 'Warehouse B',
    connectivity: Connectivity.Offline,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '0.9',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.1',
      },
      {
        targetName: 'claim',
        currentVersion: '1.0',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.0',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.1',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.0',
      },
    ],
    jobId: null,
    jobStatus: null,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322013',
    siteName: 'Distribution Center C',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.4',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.4',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.1',
      },
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.2',
      },
      {
        targetName: 'claim',
        currentVersion: '1.3',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.2',
      },
    ],
    jobId: 'JOB002XYZ',
    jobStatus: JobStatus.InProgress,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [
        {
          targetName: 'ESP32_firmware',
          updateVersion: '1.5',
        },
        {
          targetName: 'LTEmodule',
          updateVersion: '2.3',
        },
      ],
    },
  },
  {
    thingId: 'FCTC26061322014',
    siteName: 'Retail Store D',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.2',
      },
      {
        targetName: 'claim',
        currentVersion: '1.2',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.3',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.2',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.1',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.1',
      },
    ],
    jobId: 'JOB003DEF',
    jobStatus: JobStatus.Failed,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322015',
    siteName: 'Office Building E',
    connectivity: Connectivity.Offline,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.0',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.2',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.1',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.0',
      },
      {
        targetName: 'claim',
        currentVersion: '1.1',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.0',
      },
    ],
    jobId: null,
    jobStatus: null,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322016',
    siteName: 'Data Center F',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.5',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.5',
      },
      {
        targetName: 'claim',
        currentVersion: '1.3',
      },
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.3',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.2',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.4',
      },
    ],
    jobId: 'JOB004GHI',
    jobStatus: JobStatus.Queued,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [
        {
          targetName: 'RX651_CHL',
          updateVersion: '1.6',
        },
      ],
    },
  },
  {
    thingId: 'FCTC26061322017',
    siteName: 'Research Lab G',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.3',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.2',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.4',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.3',
      },
      {
        targetName: 'claim',
        currentVersion: '1.2',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.2',
      },
    ],
    jobId: 'JOB005JKL',
    jobStatus: JobStatus.Success,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322018',
    siteName: 'Testing Facility H',
    connectivity: Connectivity.Offline,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '0.8',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.0',
      },
      {
        targetName: 'claim',
        currentVersion: '0.9',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '0.9',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.0',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '1.9',
      },
    ],
    jobId: 'JOB006MNO',
    jobStatus: JobStatus.TimedOut,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322019',
    siteName: 'Production Line I',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.6',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.6',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.3',
      },
      {
        targetName: 'claim',
        currentVersion: '1.4',
      },
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.4',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.5',
      },
    ],
    jobId: 'JOB007PQR',
    jobStatus: JobStatus.InProgress,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [
        {
          targetName: 'ESP32_firmware',
          updateVersion: '1.7',
        },
        {
          targetName: 'claim',
          updateVersion: '1.5',
        },
        {
          targetName: 'ESP32_filesystem',
          updateVersion: '1.5',
        },
      ],
    },
  },
  {
    thingId: 'FCTC26061322020',
    siteName: 'Quality Control J',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.4',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.2',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.5',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.2',
      },
      {
        targetName: 'claim',
        currentVersion: '1.3',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.3',
      },
    ],
    jobId: null,
    jobStatus: null,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322021',
    siteName: 'Assembly Line K',
    connectivity: Connectivity.Offline,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.1',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.3',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.1',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.3',
      },
      {
        targetName: 'claim',
        currentVersion: '1.2',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.1',
      },
    ],
    jobId: 'JOB008STU',
    jobStatus: JobStatus.Failed,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322011',
    siteName: 'Manufacturing Plant A',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.1',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.3',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.3',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.1',
      },
      {
        targetName: 'claim',
        currentVersion: '1.2',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.1',
      },
    ],
    jobId: 'JOB001ABC',
    jobStatus: JobStatus.Success,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322012',
    siteName: 'Warehouse B',
    connectivity: Connectivity.Offline,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '0.9',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.1',
      },
      {
        targetName: 'claim',
        currentVersion: '1.0',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.0',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.1',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.0',
      },
    ],
    jobId: null,
    jobStatus: null,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322013',
    siteName: 'Distribution Center C',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.4',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.4',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.1',
      },
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.2',
      },
      {
        targetName: 'claim',
        currentVersion: '1.3',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.2',
      },
    ],
    jobId: 'JOB002XYZ',
    jobStatus: JobStatus.InProgress,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [
        {
          targetName: 'ESP32_firmware',
          updateVersion: '1.5',
        },
        {
          targetName: 'LTEmodule',
          updateVersion: '2.3',
        },
      ],
    },
  },
  {
    thingId: 'FCTC26061322014',
    siteName: 'Retail Store D',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.2',
      },
      {
        targetName: 'claim',
        currentVersion: '1.2',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.3',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.2',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.1',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.1',
      },
    ],
    jobId: 'JOB003DEF',
    jobStatus: JobStatus.Failed,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322015',
    siteName: 'Office Building E',
    connectivity: Connectivity.Offline,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.0',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.2',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.1',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.0',
      },
      {
        targetName: 'claim',
        currentVersion: '1.1',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.0',
      },
    ],
    jobId: null,
    jobStatus: null,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322016',
    siteName: 'Data Center F',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.5',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.5',
      },
      {
        targetName: 'claim',
        currentVersion: '1.3',
      },
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.3',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.2',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.4',
      },
    ],
    jobId: 'JOB004GHI',
    jobStatus: JobStatus.Queued,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [
        {
          targetName: 'RX651_CHL',
          updateVersion: '1.6',
        },
      ],
    },
  },
  {
    thingId: 'FCTC26061322017',
    siteName: 'Research Lab G',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.3',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.2',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.4',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.3',
      },
      {
        targetName: 'claim',
        currentVersion: '1.2',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.2',
      },
    ],
    jobId: 'JOB005JKL',
    jobStatus: JobStatus.Success,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322018',
    siteName: 'Testing Facility H',
    connectivity: Connectivity.Offline,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '0.8',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.0',
      },
      {
        targetName: 'claim',
        currentVersion: '0.9',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '0.9',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.0',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '1.9',
      },
    ],
    jobId: 'JOB006MNO',
    jobStatus: JobStatus.TimedOut,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322019',
    siteName: 'Production Line I',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.6',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.6',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.3',
      },
      {
        targetName: 'claim',
        currentVersion: '1.4',
      },
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.4',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.5',
      },
    ],
    jobId: 'JOB007PQR',
    jobStatus: JobStatus.InProgress,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [
        {
          targetName: 'ESP32_firmware',
          updateVersion: '1.7',
        },
        {
          targetName: 'claim',
          updateVersion: '1.5',
        },
        {
          targetName: 'ESP32_filesystem',
          updateVersion: '1.5',
        },
      ],
    },
  },
  {
    thingId: 'FCTC26061322020',
    siteName: 'Quality Control J',
    connectivity: Connectivity.Online,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.4',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.2',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.5',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.2',
      },
      {
        targetName: 'claim',
        currentVersion: '1.3',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.3',
      },
    ],
    jobId: null,
    jobStatus: null,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
  {
    thingId: 'FCTC26061322021',
    siteName: 'Assembly Line K',
    connectivity: Connectivity.Offline,
    targets: [
      {
        targetName: 'ESP32_filesystem',
        currentVersion: '1.1',
      },
      {
        targetName: 'ESP32_firmware',
        currentVersion: '1.3',
      },
      {
        targetName: 'AmazonRootCA1',
        currentVersion: '1.1',
      },
      {
        targetName: 'RX651_CHL',
        currentVersion: '1.3',
      },
      {
        targetName: 'claim',
        currentVersion: '1.2',
      },
      {
        targetName: 'LTEmodule',
        currentVersion: '2.1',
      },
    ],
    jobId: 'JOB008STU',
    jobStatus: JobStatus.Failed,
    ongoingJobExecutions: {
      completedAt: Date.now(),
      targets: [],
    },
  },
];
