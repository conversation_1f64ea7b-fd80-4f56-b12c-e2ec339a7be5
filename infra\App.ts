/* eslint-disable class-methods-use-this */
import { ZephyrApp } from '@zephyr/backend-lib-infrastructure';
import { Environment } from 'aws-cdk-lib';

import { RepositoryName } from '../src/enum';

import { CjcOtaJobServiceStack } from './stack/CjcOtaStack';
import { Configuration } from './Configuration';
import { Helper } from './Helper';

import {  OTAJobStatusSyncStack } from './stack/OTAJobStatusSyncStack';

/**
 * The AWS application of this project.
 */
export class App {
  private zephyrApp: ZephyrApp;

  /**
   * Creates an application with the specified properties.
   * @param props The properties.
   */
  public constructor() {
    this.zephyrApp = ZephyrApp.createApp({ appName: RepositoryName.AhpCjcOtaJob });

    const stageFromContext = this.zephyrApp.node.tryGetContext('stage');
    Helper.setupProcessEnv(stageFromContext);
    const config = new Configuration(stageFromContext);
    const stackEnv: Environment = config.IS_LOCAL
      ? {}
      : {
          region: config.AWS_REGION,
          account: config.AWS_ACCOUNT,
        };

    new CjcOtaJobServiceStack(this.zephyrApp.stage, config, Helper.buildId(RepositoryName.AhpCjcOtaJob, 'Stack'), {
      env: stackEnv,
    });

    new OTAJobStatusSyncStack(this.zephyrApp.stage,config,Helper.buildId(RepositoryName.OTAJobStatusSync,'stack'),{
       env : stackEnv
    })
  }
}
