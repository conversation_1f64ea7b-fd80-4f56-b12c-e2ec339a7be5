import { specifiedDirectives, DocumentNode } from 'graphql';
import { printSchemaWithDirectives } from '@graphql-tools/utils';
import gql from 'graphql-tag';
import { buildSubgraphSchema as apolloBuildSubgraphSchema } from '@apollo/subgraph';
import { addResolversToSchema, GraphQLResolverMap } from 'apollo-graphql';
import { buildSchemaSync, BuildSchemaOptions, createResolversMap } from 'type-graphql';

export function buildSubgraphSchema(
  options: Omit<BuildSchemaOptions, 'skipCheck'>,
  referenceResolvers?: GraphQLResolverMap<any>,
) {
  const schema = buildSchemaSync({
    ...options,
    directives: [...specifiedDirectives, ...(options.directives || [])],
    skipCheck: true,
    validate: { forbidUnknownValues: false },
  });

  const federatedSchema = apolloBuildSubgraphSchema({
    typeDefs: gql(printSchemaWithDirectives(schema)) as DocumentNode,
    resolvers: createResolversMap(schema) as any,
  });

  if (referenceResolvers) {
    addResolversToSchema(federatedSchema, referenceResolvers);
  }
  return federatedSchema;
}
