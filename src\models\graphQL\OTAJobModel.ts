/* eslint-disable max-classes-per-file */
import { ObjectType, InputType, Field } from 'type-graphql';
import { AhpTargetFirmwareNames, TargetFirmwareNames, TargetUpdateOperations } from '../../enum';

@InputType()
export class TargetVersions {
  @Field(() => String)
  firmwareName: TargetFirmwareNames;

  @Field(() => String)
  version: string;
}

@ObjectType()
export class CreateFirmwareJobResponse {
  @Field(() => [String])
  jobIds: string[];
}

@ObjectType()
export class OtaFirmwareUpdateErrorResponse {
  @Field(() => String)
  message: string;
}

@ObjectType()
export class CreateFirmWareJobOutput {
  @Field(() => CreateFirmwareJobResponse, { nullable: true })
  data: CreateFirmwareJobResponse;

  @Field(() => OtaFirmwareUpdateErrorResponse, { nullable: true })
  error?: OtaFirmwareUpdateErrorResponse;
}

@InputType()
export class CreateFirmWareJobInput {
  @Field(() => [String])
  targetThings: string[];

  @Field(() => [TargetVersions])
  targetVersions: TargetVersions[];

  @Field(() => TargetUpdateOperations, { nullable: false })
  targetUpdateOperation: TargetUpdateOperations;

  @Field(() => String, { nullable: true, defaultValue: '' })
  lteModuleUrl?: string;
}

@InputType()
export class AhpTargetVersions {
  @Field(() => String)
  firmwareName: AhpTargetFirmwareNames | TargetFirmwareNames;

  @Field(() => String)
  version: string;
}

@InputType()
export class CreateAHPFirmWareJobInput {
  @Field(() => [String])
  targetThings: string[];

  @Field(() => AhpTargetVersions)
  targetVersions: AhpTargetVersions;
}

@ObjectType()
export class CreateAHPFirmWareJobOutput {
  @Field(() => CreateFirmwareJobResponse, { nullable: true })
  data: CreateFirmwareJobResponse;

  @Field(() => OtaFirmwareUpdateErrorResponse, { nullable: true })
  error?: OtaFirmwareUpdateErrorResponse;
}
