<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
  <file path="C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts">
    <testCase name="CodeSigningService getSigningProfile should return correct profile for ESP32_firmware" duration="4"/>
    <testCase name="CodeSigningService getSigningProfile should return correct profile for ESP32_filesystem" duration="0"/>
    <testCase name="CodeSigningService getSigningProfile should return correct profile for RX651_CHL" duration="1"/>
    <testCase name="CodeSigningService getSigningProfile should return null for unknown file type" duration="1"/>
    <testCase name="CodeSigningService startSigningJob should start signing job successfully" duration="44">
      <failure message="Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)"><![CDATA[Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)

Expected number of calls: 1
Received number of calls: 0
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:92:30)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)]]></failure>
    </testCase>
    <testCase name="CodeSigningService startSigningJob should throw error when version is missing" duration="14"/>
    <testCase name="CodeSigningService startSigningJob should handle signing job failure" duration="8">
      <failure message="Error: expect(received).rejects.toThrow(expected)"><![CDATA[Error: expect(received).rejects.toThrow(expected)

Expected substring: "Signing failed"
Received message:   "Cannot read properties of undefined (reading 'jobId')"

      71 |
      72 |       logger.info('Signing job started successfully', {
    > 73 |         jobId: response.jobId,
         |                         ^
      74 |         source: `s3://${request.bucketName}/${request.key}`,
      75 |         destination: `s3://${request.bucketName}/${request.destinationPrefix}`,
      76 |         profile: request.profileName,

      at CodeSigningService.jobId [as startSigningJob] (src/services/CodeSigningService.ts:73:25)
      at Object.<anonymous> (src/__tests__/services/CodeSigningService.test.ts:124:7)
    at Object.toThrow (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\expect\build\index.js:218:22)
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:125:18)
    at Promise.then.completed (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:444:34)]]></failure>
    </testCase>
    <testCase name="CodeSigningService monitorBatchSigningJobs should handle empty job requests" duration="1"/>
    <testCase name="CodeSigningService monitorBatchSigningJobs should monitor and process successful jobs" duration="6">
      <failure message="TypeError: Cannot read properties of undefined (reading &apos;Succeeded&apos;)"><![CDATA[TypeError: Cannot read properties of undefined (reading 'Succeeded')
    at CodeSigningService.Succeeded [as monitorBatchSigningJobs] (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\services\CodeSigningService.ts:148:46)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:162:7)]]></failure>
    </testCase>
    <testCase name="CodeSigningService monitorBatchSigningJobs should handle failed jobs" duration="6">
      <failure message="Error: expect(received).resolves.not.toThrow()"><![CDATA[Error: expect(received).resolves.not.toThrow()

Received promise rejected instead of resolved
Rejected to value: [TypeError: Cannot read properties of undefined (reading 'Succeeded')]
    at expect (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\expect\build\index.js:113:15)
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:179:13)
    at Promise.then.completed (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:444:34)]]></failure>
    </testCase>
    <testCase name="CodeSigningService monitorBatchSigningJobs should handle job status check errors" duration="6">
      <failure message="Error: expect(received).resolves.not.toThrow()"><![CDATA[Error: expect(received).resolves.not.toThrow()

Received promise rejected instead of resolved
Rejected to value: [TypeError: Cannot read properties of undefined (reading 'Succeeded')]
    at expect (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\expect\build\index.js:113:15)
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:188:13)
    at Promise.then.completed (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:444:34)]]></failure>
    </testCase>
    <testCase name="CodeSigningService delay should delay for specified time" duration="2"/>
  </file>
</testExecutions>