<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
  <file path="C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts">
    <testCase name="CodeSigningService getSigningProfile should return correct profile for ESP32_firmware" duration="0"/>
    <testCase name="CodeSigningService getSigningProfile should return correct profile for ESP32_filesystem" duration="0"/>
    <testCase name="CodeSigningService getSigningProfile should return correct profile for RX651_CHL" duration="0"/>
    <testCase name="CodeSigningService getSigningProfile should return null for unknown file type" duration="0"/>
    <testCase name="CodeSigningService startSigningJob should start signing job successfully" duration="0"/>
    <testCase name="CodeSigningService startSigningJob should throw error when version is missing" duration="0"/>
    <testCase name="CodeSigningService startSigningJob should handle signing job failure" duration="0"/>
    <testCase name="CodeSigningService monitorBatchSigningJobs should handle empty job requests" duration="0"/>
    <testCase name="CodeSigningService monitorBatchSigningJobs should monitor and process successful jobs" duration="0"/>
    <testCase name="CodeSigningService monitorBatchSigningJobs should handle failed jobs" duration="0"/>
    <testCase name="CodeSigningService monitorBatchSigningJobs should handle job status check errors" duration="70">
      <failure message="Error: expect(received).resolves.not.toThrow()"><![CDATA[Error: expect(received).resolves.not.toThrow()

Received promise rejected instead of resolved
Rejected to value: [TypeError: Cannot read properties of undefined (reading 'Succeeded')]
    at expect (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\expect\build\index.js:113:15)
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:187:13)
    at Promise.then.completed (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:444:34)]]></failure>
    </testCase>
    <testCase name="CodeSigningService delay should delay for specified time" duration="0"/>
  </file>
</testExecutions>