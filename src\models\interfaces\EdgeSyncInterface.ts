export interface SyncResult {
  syncedCount: number;
  failedCount: number;
  totalProcessed: number;
  details: {
    successful: string[];
    failed: Array<{ edgeId: string; error: string }>;
  };
}

export interface CurrentJob {
  jobId: string | null;
  jobStatus: string | null;
  completedAt: Date | null;
  targets: Targets[];
}

export interface EdgeJobDetails {
  currentJob?: CurrentJob;
}

export interface EdgeSyncConfig {
  batchSize: number;
  maxRetries: number;
  includeAssets: boolean;
  filters: any;
}

export interface Edge {
  edgeNodeId: string;
  siteName: string;
  model: string;
  edgeId: string | null;
  isOnline: boolean | null;
  allTargets: AllTargets[];
  currentJob?: CurrentJob;
  createdAt?: Date | null;
  updatedAt?: Date | null;
}

export interface Targets {
  targetName: string;
  version: string;
}

export interface AllTargets extends Targets {
  lastUpdated: string | null;
}

export type IAllEdges = Partial<Edge>;
