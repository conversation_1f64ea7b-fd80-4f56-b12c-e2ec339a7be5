import { CodeSigningService } from '../../services/CodeSigningService';
import { SignerClient, StartSigningJobCommand, DescribeSigningJobCommand } from '@aws-sdk/client-signer';
import { S3Client, CopyObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';

// Mock the AWS SDK modules
jest.mock('@aws-sdk/client-signer');
jest.mock('@aws-sdk/client-s3');
jest.mock('../../enum/CjcFirmawareEnum', () => ({
  codeSigningProfiles: {
    'ESP32_firmware': 'TestProfile1',
    'ESP32_filesystem': 'TestProfile2',
    'RX651_CHL': 'TestProfile3'
  },
  SigningJobStatus: {
    InProgress: 'InProgress',
    Failed: 'Failed',
    Succeeded: 'Succeeded',
  }
}));

// Create mock functions
const mockSignerSend = jest.fn();
const mockS3Send = jest.fn();

// Mock the constructors to return objects with send methods
(SignerClient as jest.Mock).mockImplementation(() => ({
  send: mockSignerSend,
}));

(S3Client as jest.Mock).mockImplementation(() => ({
  send: mockS3Send,
}));

// Mock the command constructors
(StartSigningJobCommand as unknown as jest.Mock).mockImplementation((params) => ({ params }));
(DescribeSigningJobCommand as unknown as jest.Mock).mockImplementation((params) => ({ params }));
(CopyObjectCommand as unknown as jest.Mock).mockImplementation((params) => ({
  params,
}));
(DeleteObjectCommand as unknown as jest.Mock).mockImplementation((params) => ({
  params,
}));

describe('CodeSigningService', () => {
  let codeSigningService: CodeSigningService;
	let mockSigner: any;
  let mockS3: any;

  beforeEach(() => {
    jest.clearAllMocks();
    mockSignerSend.mockReset();
    mockS3Send.mockReset();
    mockSigner = { send: mockSignerSend };
    mockS3 = { send: mockS3Send };
    codeSigningService = new CodeSigningService(mockSigner, mockS3);
  });

  describe('getSigningProfile', () => {
    it('should return correct profile for ESP32_firmware', () => {
      const result = codeSigningService.getSigningProfile('path/ESP32_firmware.bin', 'test-bucket');
      expect(result).toBe('TestProfile1');
    });

    it('should return correct profile for ESP32_filesystem', () => {
      const result = codeSigningService.getSigningProfile('path/ESP32_filesystem.bin', 'test-bucket');
      expect(result).toBe('TestProfile2');
    });

    it('should return correct profile for RX651_CHL', () => {
      const result = codeSigningService.getSigningProfile('path/RX651_CHL.mot', 'test-bucket');
      expect(result).toBe('TestProfile3');
    });

    it('should return null for unknown file type', () => {
      const result = codeSigningService.getSigningProfile('path/unknown_file.txt', 'test-bucket');
      expect(result).toBeNull();
    });
  });

  describe('startSigningJob', () => {
    const mockRequest = {
      bucketName: 'test-bucket',
      key: 'path/ESP32_firmware.bin',
      version: 'version123',
      profileName: 'TestProfile1',
      destinationPrefix: 'signed/',
    };

    it('should start signing job successfully', async () => {
      const mockJobId = 'job-123';
      mockSignerSend.mockResolvedValue({ jobId: mockJobId });

      const result = await codeSigningService.startSigningJob(mockRequest);

      expect(result).toBe(mockJobId);
      expect(mockSignerSend).toHaveBeenCalledTimes(1);
      expect(StartSigningJobCommand).toHaveBeenCalledWith({
        source: {
          s3: {
            bucketName: mockRequest.bucketName,
            key: mockRequest.key,
            version: mockRequest.version,
          },
        },
        destination: {
          s3: {
            bucketName: mockRequest.bucketName,
            prefix: mockRequest.destinationPrefix,
          },
        },
        profileName: mockRequest.profileName,
      });
    });

    it('should throw error when version is missing', async () => {
      const requestWithoutVersion = { ...mockRequest, version: undefined };

      await expect(codeSigningService.startSigningJob(requestWithoutVersion))
        .rejects.toThrow('version is required for StartSigningJobCommand');
      
      expect(mockSignerSend).not.toHaveBeenCalled();
    });

    it('should handle signing job failure', async () => {
      const error = new Error('Signing failed');
      mockSignerSend.mockRejectedValue(error);

      await expect(codeSigningService.startSigningJob(mockRequest))
        .rejects.toThrow('Signing failed');
      
      expect(mockSignerSend).toHaveBeenCalledTimes(1);
    });
  });

  describe('monitorBatchSigningJobs', () => {
    const mockJobRequests = [
      {
        jobId: 'job-1',
        request: {
          bucketName: 'test-bucket',
          key: 'GatewayDocs/OTA/ESP32/v1.0.0/ESP32_firmware.bin',
          version: 'v1',
          profileName: 'TestProfile1',
          destinationPrefix: 'signed/',
        }
      }
    ];

    it('should handle empty job requests', async () => {
      await expect(codeSigningService.monitorBatchSigningJobs([]))
        .resolves.not.toThrow();
      
      expect(mockSignerSend).not.toHaveBeenCalled();
    });

    it('should monitor and process successful jobs', async () => {
      // Mock successful job status - this is the AWS SDK response format
      mockSignerSend.mockResolvedValue({
        status: 'Succeeded',
        signedObject: { s3: { key: 'signed/job-1-uuid' } }
      });

      // Mock S3 operations
      mockS3Send.mockResolvedValue({});

      await codeSigningService.monitorBatchSigningJobs(mockJobRequests);

      expect(mockSignerSend).toHaveBeenCalledWith(
        expect.objectContaining({ params: { jobId: 'job-1' } })
      );
      expect(mockS3Send).toHaveBeenCalledWith(
        expect.objectContaining({ params: expect.objectContaining({ Bucket: 'test-bucket' }) })
      );
    });

    it('should handle failed jobs', async () => {
      // Mock failed job status - this is the AWS SDK response format
      mockSignerSend.mockResolvedValue({
        status: 'Failed',
        statusReason: 'Certificate expired'
      });

      await expect(codeSigningService.monitorBatchSigningJobs(mockJobRequests))
        .resolves.not.toThrow();

      expect(mockSignerSend).toHaveBeenCalledTimes(1);
    });

    it('should handle job status check errors', async () => {
      mockSignerSend.mockRejectedValue(new Error('API Error'));

      await expect(codeSigningService.monitorBatchSigningJobs(mockJobRequests))
        .resolves.not.toThrow();
      
      expect(mockSignerSend).toHaveBeenCalledTimes(1);
    });
  });

  describe('delay', () => {
    it('should delay for specified time', async () => {
      jest.useFakeTimers();
      
      const delayPromise = (codeSigningService as any).delay(1000);
      
      // Fast-forward time
      jest.advanceTimersByTime(1000);
      
      await expect(delayPromise).resolves.toBeUndefined();
      
      jest.useRealTimers();
    });
  });
});
