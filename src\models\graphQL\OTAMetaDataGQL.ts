import { ObjectType, Field, InputType, Int } from 'type-graphql';
import { Connectivity, JobStatus, TargetDirectories, TargetFirmwareNames } from '../../enum';

@ObjectType()
export class ResponseError {
  @Field(() => String)
  message: string;
}

@ObjectType()
export class Target {
  @Field(() => String)
  targetName: string;

  @Field(() => String)
  updateVersion: string;
}

@ObjectType()
export class AllTarget {
  @Field(() => String)
  targetName: string;

  @Field(() => String)
  currentVersion: string;
}

@ObjectType()
export class CurrentJobDetails {
  @Field(() => [Target])
  targets: Target[];

  @Field(() => Date, { nullable: true })
  completedAt: Date;
}

@ObjectType()
export class OtaGatewayJobInfo {
  @Field(() => String)
  thingId: string;

  @Field(() => String)
  siteName: string;

  @Field(() => Connectivity)
  connectivity: Connectivity;

  @Field(() => [AllTarget])
  allTargets: AllTarget[];

  @Field(() => String, { nullable: true })
  jobId: string;

  @Field(() => JobStatus, { nullable: true })
  jobStatus: JobStatus;

  @Field(() => CurrentJobDetails)
  currentJobDetails: CurrentJobDetails;
}

@ObjectType()
export class OtaMetadataResponse {
  @Field(() => [OtaGatewayJobInfo])
  data: OtaGatewayJobInfo[];

  @Field()
  totalRecords: number;

  @Field(() => Int)
  currentPage: number;

  @Field(() => Int)
  totalPages: number;

  @Field(() => Boolean)
  hasNextPage: boolean;

  @Field(() => Boolean)
  hasPreviousPage: boolean;

  @Field(() => ResponseError, { nullable: true })
  error?: ResponseError;
}

@InputType()
export class PaginationInput {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => String, { defaultValue: 'updatedAt', nullable: true })
  sortBy? = 'updatedAt';

  @Field(() => String, { defaultValue: 'asc', nullable: true })
  sortOrder?: 'asc' | 'desc' = 'asc';
}
@InputType()
export class FilterInput {
  @Field(() => [String], { nullable: true })
  searchTerm?: string[];

  @Field(() => [String], { nullable: true })
  connectivity?: Connectivity[];

  @Field(() => [String], { nullable: true })
  targetName?: TargetFirmwareNames[];

  @Field(() => [String], { nullable: true })
  jobStatus?: JobStatus[];

  @Field(() => [String], { nullable: true })
  targetVersion?: string[];
}

@InputType()
export class VersionPaginationInput {
  @Field(() => String)
  targetDirectory: TargetDirectories;
}

@ObjectType()
export class VersionAvailabilityInfo {
  @Field(() => String)
  version: string;

  @Field(() => [String])
  availableFiles: string[];
}
@ObjectType()
export class TargetVersionResponse {
  @Field(() => [VersionAvailabilityInfo])
  versionDetails: VersionAvailabilityInfo[];

  @Field(() => ResponseError, { nullable: true })
  error?: ResponseError;
}

@ObjectType()
export class SyncEdgesResponse {
  @Field()
  message: string;

  @Field(() => Int)
  syncedCount: number;

  @Field(() => Int)
  failedCount: number;

  @Field(() => Int)
  durationMs: number;

  @Field(() => Int)
  successfulEdges: number;

  @Field(() => Int)
  failedEdges: number;

  @Field({ nullable: true })
  error?: string;
}

@InputType()
export class EdgeSyncInput {
  @Field(() => [String])
  edgeModels: string[];

  @Field(() => Int, { defaultValue: 50 })
  batchSize: number;

  @Field(() => Int, { defaultValue: 3 })
  maxRetries: number;

  @Field(() => Boolean, { defaultValue: false })
  includeAssets: boolean;
}

@ObjectType()
export class CurrentTargetVersionsPagination {
  @Field(() => [String])
  versions!: string[];

  @Field(() => Int)
  total!: number;

  @Field(() => Int)
  page!: number;

  @Field(() => Int)
  pageSize!: number;

  @Field(() => Int)
  totalPages!: number;
}
