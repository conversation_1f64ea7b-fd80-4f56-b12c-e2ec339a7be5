{"root": true, "ignorePatterns": ["node_modules/", "dist/", "coverage/", "tools/", "cdk.out/"], "plugins": ["@typescript-eslint", "sonarjs", "prettier", "jest"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "tsconfigRootDir": "tsconfig.json"}, "env": {"jest/globals": true, "es2020": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier", "airbnb-base", "plugin:sonarjs/recommended", "plugin:import/typescript", "plugin:import/errors", "plugin:import/warnings", "plugin:prettier/recommended"], "rules": {"no-underscore-dangle": "off", "no-restricted-syntax": "off", "no-unused-vars": "off", "no-undef": "off", "no-shadow": "off", "no-new": "off", "import/no-internal-modules": ["error", {"forbid": []}], "import/no-unresolved": "off", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index", "object"]}], "class-methods-use-this": "off", "import/ignore": "off", "import/prefer-default-export": "off", "import/extensions": "off", "import/no-extraneous-dependencies": "off", "import/no-cycle": "off", "no-use-before-define": "off", "no-dupe-class-members": "off", "array-callback-return": "off", "consistent-return": "off", "guard-for-in": "off", "@typescript-eslint/no-use-before-define": ["error"], "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "ignoreRestSiblings": true}], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-shadow": "error", "@typescript-eslint/explicit-member-accessibility": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-parameter-properties": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-var-requires": "off", "max-classes-per-file": [0], "no-console": [0], "prettier/prettier": ["warn", {"trailingComma": "all", "tabWidth": 2, "semi": true, "singleQuote": true, "bracketSpacing": true, "eslintIntegration": true, "printWidth": 120}]}, "settings": {"import/parsers": {"@typescript-eslint/parser": [".ts", ".tsx", ".js", ".jsx"]}, "import/resolver": {"typescript": {"project": "tsconfig.json"}}}}