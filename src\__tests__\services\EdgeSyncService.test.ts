import { EdgeSyncService } from '../../services/EdgeSyncService';
import { EdgeListingService } from '../../services/EdgeListingService';
import { EdgeModel } from '../../schemas';
import { logger } from '../../helpers';
import { JobStatus, TargetFirmwareNames, JobExecutionStatus } from '../../enum';
import { EdgeEntity } from '../../models/entities/EdgeEntity';
import { CurrentJob, EdgeJobDetails } from '../../models';

jest.mock('../../services/EdgeListingService');
jest.mock('../../schemas', () => ({
  EdgeModel: {
    findOne: jest.fn(),
    findOneAndUpdate: jest.fn(),
    create: jest.fn(),
    countDocuments: jest.fn(),
  },
}));
jest.mock('../../helpers', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

describe('EdgeSyncService', () => {
  let edgeSyncService: EdgeSyncService;
  let mockEdgeListingService: jest.Mocked<EdgeListingService>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockEdgeListingService = new EdgeListingService() as jest.Mocked<EdgeListingService>;
    edgeSyncService = new EdgeSyncService({
      batchSize: 10,
      maxRetries: 3,
      includeAssets: true,
      filters: {},
    });
    (edgeSyncService as any).edgeListingService = mockEdgeListingService;
  });

  describe('syncAllEdgesToMongo', () => {
    it('should return null if config is not provided', async () => {
      edgeSyncService = new EdgeSyncService();
      const result = await edgeSyncService.syncAllEdgesToMongo();
      expect(result).toBeNull();
    });

    it('should process edges in batches and return sync results', async () => {
      const mockEdges: EdgeEntity[] = [
        {
          id: 'edge1',
          edgeId: 'edge-001',
          name: 'Edge 1',
          isOnline: true,
          model: 'Model A',
          esp32Version: '1.0.0',
          rx651Version: '2.0.0',
          lteVersion: '3.0.0',
          domain: 'edge',
          assets: [] as any,
          isDeleted: false,

          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'edge2',
          edgeId: 'edge-002',
          name: 'Edge 2',
          isOnline: false,
          model: 'Model B',
          esp32Version: '1.1.0',
          rx651Version: '2.1.0',
          lteVersion: '3.1.0',
          domain: 'edge',
          assets: [] as any,
          isDeleted: false,

          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockEdgeListingService.getEdgesBasedOnModel.mockResolvedValueOnce(mockEdges);
      mockEdgeListingService.getEdgesBasedOnModel.mockResolvedValueOnce([]);

      (EdgeModel.findOne as jest.Mock).mockImplementation(() => ({
        lean: jest.fn().mockResolvedValue(null),
      }));

      (EdgeModel.create as jest.Mock).mockResolvedValue({});

      const result = await edgeSyncService.syncAllEdgesToMongo();

      expect(result).toBeDefined();
      expect(result?.syncedCount).toBe(2);
      expect(result?.failedCount).toBe(0);
      expect(result?.totalProcessed).toBe(2);
      expect(mockEdgeListingService.getEdgesBasedOnModel).toHaveBeenCalledTimes(2);
      expect(EdgeModel.create).toHaveBeenCalledTimes(2);
    });

    it('should handle errors during sync process', async () => {
      const mockEdges: EdgeEntity[] = [
        {
          id: 'edge1',
          edgeId: 'edge-001',
          name: 'Edge 1',
          isOnline: true,
          model: 'Model A',
          domain: 'edge',
          assets: [] as any,
          isDeleted: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockEdgeListingService.getEdgesBasedOnModel.mockResolvedValueOnce(mockEdges);
      mockEdgeListingService.getEdgesBasedOnModel.mockResolvedValueOnce([]);

      const error = new Error('Database connection failed');
      (EdgeModel.findOne as jest.Mock).mockImplementation(() => {
        throw error;
      });

      const result = await edgeSyncService.syncAllEdgesToMongo();

      expect(result).toBeDefined();
      expect(result?.syncedCount).toBe(0);
      expect(result?.failedCount).toBe(1);
      expect(result?.totalProcessed).toBe(1);
      expect(result?.details.failed).toContainEqual({
        edgeId: 'edge-001',
        error: 'Database connection failed',
      });
      expect(logger.error).toHaveBeenCalledWith('Failed to sync edge: edge1', { error: expect.any(Error) });
    });
  });

  describe('updateEdgeDataByEdgeId', () => {
    it('should update edge job details successfully', async () => {
      const edgeId = 'edge-001';
      const edgeJobDetails: EdgeJobDetails = {
        currentJob: {
          jobId: 'job-123',
          jobStatus: JobStatus.InProgress,
          targets: [
            {
              targetName: TargetFirmwareNames.ESP32_firmware,
              version: '1.0.0',
            },
          ],
          completedAt: null,
        },
      };

      const mockUpdatedEdge = {
        edgeId,
        currentJob: edgeJobDetails.currentJob,
        toObject: jest.fn().mockReturnValue({ edgeId }),
      };

      (EdgeModel.findOneAndUpdate as jest.Mock).mockResolvedValue(mockUpdatedEdge);

      const result = await EdgeSyncService.updateEdgeDataByEdgeId(edgeId, edgeJobDetails);

      expect(result).toBe(mockUpdatedEdge);
      expect(EdgeModel.findOneAndUpdate).toHaveBeenCalledWith(
        { edgeId },
        {
          $set: {
            currentJob: edgeJobDetails.currentJob,
          },
        },
        { new: true },
      );
    });

    it('should throw error when jobId is missing', async () => {
      const edgeId = 'edge-001';
      const edgeJobDetails: EdgeJobDetails = {
        currentJob: {
          jobId: '',
          jobStatus: JobStatus.InProgress,
          targets: [
            {
              targetName: TargetFirmwareNames.ESP32_firmware,
              version: '1.0.0',
            },
          ],
          completedAt: null,
        },
      };

      await expect(EdgeSyncService.updateEdgeDataByEdgeId(edgeId, edgeJobDetails)).rejects.toThrow(
        'jobId is required and cannot be null or empty',
      );
    });

    it('should throw error when targets array is empty', async () => {
      const edgeId = 'edge-001';
      const edgeJobDetails: EdgeJobDetails = {
        currentJob: {
          jobId: 'job-123',
          jobStatus: JobStatus.InProgress,
          targets: [],
          completedAt: null,
        },
      };

      await expect(EdgeSyncService.updateEdgeDataByEdgeId(edgeId, edgeJobDetails)).rejects.toThrow(
        'targets array is required and cannot be empty',
      );
    });

    it('should throw error when jobStatus is invalid', async () => {
      const edgeId = 'edge-001';
      const edgeJobDetails: EdgeJobDetails = {
        currentJob: {
          jobId: 'job-123',
          jobStatus: 'InvalidStatus' as JobStatus,
          targets: [
            {
              targetName: TargetFirmwareNames.ESP32_firmware,
              version: '1.0.0',
            },
          ],
          completedAt: null,
        },
      };

      await expect(EdgeSyncService.updateEdgeDataByEdgeId(edgeId, edgeJobDetails)).rejects.toThrow(/Invalid jobStatus/);
    });

    it('should return null when edge is not found', async () => {
      const edgeId = 'non-existent-edge';
      const edgeJobDetails: EdgeJobDetails = {
        currentJob: {
          jobId: 'job-123',
          jobStatus: JobStatus.InProgress,
          targets: [
            {
              targetName: TargetFirmwareNames.ESP32_firmware,
              version: '1.0.0',
            },
          ],
          completedAt: null,
        },
      };

      (EdgeModel.findOneAndUpdate as jest.Mock).mockResolvedValue(null);

      const result = await EdgeSyncService.updateEdgeDataByEdgeId(edgeId, edgeJobDetails);

      expect(result).toBeNull();
      expect(logger.warn).toHaveBeenCalledWith(`No edge found with edgeId: ${edgeId}`);
    });
  });

  describe('checkActiveJob', () => {
    it('should return true when active job exists', async () => {
      const thingId = 'edge-001';

      (EdgeModel.countDocuments as jest.Mock).mockResolvedValue(1);

      const result = await EdgeSyncService.checkActiveJob(thingId);

      expect(result).toBe(true);
      expect(EdgeModel.countDocuments).toHaveBeenCalledWith({
        edgeId: thingId,
        'currentJob.jobStatus': {
          $in: [JobStatus.Queued, JobStatus.InProgress],
        },
      });
    });

    it('should return false when no active job exists', async () => {
      const thingId = 'edge-001';

      (EdgeModel.countDocuments as jest.Mock).mockResolvedValue(0);

      const result = await EdgeSyncService.checkActiveJob(thingId);

      expect(result).toBe(false);
    });

    it('should return true when error occurs during check', async () => {
      const thingId = 'edge-001';
      const error = new Error('Database error');

      (EdgeModel.countDocuments as jest.Mock).mockRejectedValue(error);

      const result = await EdgeSyncService.checkActiveJob(thingId);

      expect(result).toBe(true);
      expect(logger.error).toHaveBeenCalledWith(`Error checking active jobs for thing ${thingId}:`, { error });
    });
  });

  describe('updateJobStatusAndVersion', () => {
    it('should update job status and merge targets correctly', async () => {
      const edgeId = 'edge-001';
      const currentJobDetails: CurrentJob = {
        jobId: 'job-123',
        jobStatus: 'SUCCEEDED',
        completedAt: new Date(),
        targets: [
          {
            targetName: TargetFirmwareNames.ESP32_firmware,
            version: '2.0.0',
          },
        ],
      };

      const existingEdge = {
        edgeId,
        allTargets: [
          {
            targetName: TargetFirmwareNames.ESP32_firmware,
            version: '1.0.0',
            lastUpdated: new Date(),
          },
          {
            targetName: TargetFirmwareNames.RX651_CHL,
            version: '1.0.0',
            lastUpdated: new Date(),
          },
        ],
      };

      (EdgeModel.findOne as jest.Mock).mockResolvedValue(existingEdge);
      (EdgeModel.findOneAndUpdate as jest.Mock).mockResolvedValue({
        ...existingEdge,
        currentJob: {
          jobId: currentJobDetails.jobId,
          jobStatus: JobExecutionStatus.SUCCEEDED,
          completedAt: currentJobDetails.completedAt,
        },
      });

      await edgeSyncService.updateJobStatusAndVersion(edgeId, currentJobDetails);

      expect(EdgeModel.findOneAndUpdate).toHaveBeenCalledWith(
        { edgeId },
        {
          $set: {
            allTargets: [
              {
                targetName: TargetFirmwareNames.ESP32_firmware,
                version: '2.0.0',
              },
              {
                targetName: TargetFirmwareNames.RX651_CHL,
                version: '1.0.0',
                lastUpdated: expect.any(Date),
              },
            ],
            currentJob: {
              jobId: currentJobDetails.jobId,
              jobStatus: JobExecutionStatus.SUCCEEDED,
              completedAt: expect.any(Date),
            },
            updatedAt: expect.any(Date),
          },
        },
      );
    });

    it('should handle missing targets in currentJobDetails', async () => {
      const edgeId = 'edge-001';
      const currentJobDetails: CurrentJob = {
        jobId: 'job-123',
        jobStatus: 'SUCCEEDED',
        completedAt: new Date(),
        targets: [],
      };

      const existingEdge = {
        edgeId,
        allTargets: [
          {
            targetName: TargetFirmwareNames.ESP32_firmware,
            version: '1.0.0',
            lastUpdated: new Date(),
          },
        ],
      };

      (EdgeModel.findOne as jest.Mock).mockResolvedValue(existingEdge);
      (EdgeModel.findOneAndUpdate as jest.Mock).mockResolvedValue({
        ...existingEdge,
        currentJob: {
          jobId: currentJobDetails.jobId,
          jobStatus: JobExecutionStatus.SUCCEEDED,
          completedAt: currentJobDetails.completedAt,
        },
      });

      await edgeSyncService.updateJobStatusAndVersion(edgeId, currentJobDetails);

      expect(EdgeModel.findOneAndUpdate).toHaveBeenCalledWith(
        { edgeId },
        {
          $set: {
            allTargets: existingEdge.allTargets,
            currentJob: {
              jobId: currentJobDetails.jobId,
              jobStatus: JobExecutionStatus.SUCCEEDED,
              completedAt: expect.any(Date),
            },
            updatedAt: expect.any(Date),
          },
        },
      );
    });

    it('should handle edge not found in database', async () => {
      const edgeId = 'edge-001';
      const currentJobDetails: CurrentJob = {
        jobId: 'job-123',
        jobStatus: 'SUCCEEDED',
        completedAt: new Date(),
        targets: [
          {
            targetName: TargetFirmwareNames.ESP32_firmware,
            version: '2.0.0',
          },
        ],
      };

      (EdgeModel.findOne as jest.Mock).mockResolvedValue(null);
      (EdgeModel.findOneAndUpdate as jest.Mock).mockResolvedValue({
        edgeId,
        currentJob: {
          jobId: currentJobDetails.jobId,
          jobStatus: JobExecutionStatus.SUCCEEDED,
          completedAt: currentJobDetails.completedAt,
        },
        allTargets: currentJobDetails.targets,
      });

      await edgeSyncService.updateJobStatusAndVersion(edgeId, currentJobDetails);

      expect(EdgeModel.findOneAndUpdate).toHaveBeenCalledWith(
        { edgeId },
        {
          $set: {
            allTargets: currentJobDetails.targets,
            currentJob: {
              jobId: currentJobDetails.jobId,
              jobStatus: JobExecutionStatus.SUCCEEDED,
              completedAt: expect.any(Date),
            },
            updatedAt: expect.any(Date),
          },
        },
      );
    });
  });
});
