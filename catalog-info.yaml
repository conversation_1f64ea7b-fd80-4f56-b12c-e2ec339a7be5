---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: ahp-service-cjc-ota-job
  description: 'A software service part of the AHP project built with Node.js, AWS CDK, TypeScript.'
  annotations:
    circleci.com/project-slug: bitbucket/carrier-digital/ahp-service-cjc-ota-job
spec:
  type: service
  lifecycle: production
  owner: group:default/carrier-digital-dpx-ahp
  system: carrier-digital-dpx-ahp-onboarding