import 'reflect-metadata';
import Express, { Application } from 'express';
import { ApolloServer } from 'apollo-server-lambda';

import { CjcOtaJobResolver, OtaMetaDataResolver } from '../resolver';
import { logger } from '../helpers/logger';
import { buildSubgraphSchema } from '../helpers/buildSubgraphSchema';
import { LoggerGraphQLData } from '../middleware/LogIO';
import { UploadCjcFirmwareResolver } from '../resolver/UploadCjcFirmwareResolver';
/**
 * Contains methods to work with Apollo Server application.
 */

export interface Context {
  event: any;
  context: any;
  expressRequest: any;
  expressResponse: any;
}

export class App {
  /**
   * An instance of the Apollo Server.
   */
  private server: ApolloServer;

  /**
   * Creates an application.
   */
  public constructor() {
    const schema = buildSubgraphSchema({
      resolvers: [CjcOtaJobResolver, UploadCjcFirmwareResolver, OtaMetaDataResolver],
      globalMiddlewares: [LoggerGraphQLData],
    });

    this.server = new ApolloServer({
      schema,
      context: ({ event, context, express }): Context => {
        return {
          event,
          context,
          expressRequest: express.req,
          expressResponse: express.res,
        };
      },
      csrfPrevention: true,
    });
  }

  /**
   * Returns a handler for the AWS Lambda.
   */
  public createLambdaHandler() {
    return this.server.createHandler({
      expressAppFromMiddleware(middleware) {
        const app: Application = Express();
        app.use(middleware);
        app.use((...args) => {
          logger.info({ message: 'Express app use', args });
        });
        return app;
      },
      expressGetMiddlewareOptions: {
        cors: {
          origin: '*',
          credentials: true,
        },
      },
    });
  }
}
