STAGE=dev

# VPC ID
VPC_ID=vpc-06e31b1b2ea42582c
CDK_DEFAULT_ACCOUNT=************
CARRIERIO_ROLE_ARN=arn:aws:iam::************:role/ZephyrPlatformRole-dev

# common
PLATFORM_NAME=zephyr
SERVICE_NAME=ahp-service-cjc-ota-job
CDK_DEFAULT_REGION=us-east-1
REGION=us-east-1
DOT_ENV_SECRET_MANAGER_ARN=arn:aws:secretsmanager:us-east-1:************:secret:ahp-service-cjc-ota-job-dot-env-file-DojSzr
HVAC_DOMAIN_NAME=api.dev.hvac.abound.carrier.io
CJC_OTA_SERVICE_BUCKET_NAME=cjc-backup-files-bucket
S3_URL_EXPIRATION_TIME=3600
CARRIERIO_ACCOUNT_ID=************
CARRIERIO_TEMP_S3_ROLEARN=arn:aws:iam::************:role/cjc-iot-job-s3-signing-bw2
DB_NAME=AHP
DB_URL='mongodb+srv://<username>:<password>@ahp-pl-0.xfqtj.mongodb.net/?retryWrites=true&w=majority&appName=OTAServiceConsumer'
SECRET_DB_KEY=ahp-dev-mongodb
SECRET_DB_ARN=arn:aws:secretsmanager:us-east-1:************:secret:ahp-dev-mongodb-rezMIi
PLATFORM_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************.YgiAYjb8GlkrJFVptu3VvNTCEdtf0kniD7YMFZujMYs
NODE_API_URL=https://node.api.dev.carrier.io/nodes/
AHP_OTA_SERVICE_BUCKET_NAME=ahp-gateway-ota
OTA_STATUS_UPDATE_QUEUE=ota_edge_job_status_update_queue
