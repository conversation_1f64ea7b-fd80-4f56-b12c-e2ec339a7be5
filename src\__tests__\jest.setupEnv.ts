import 'reflect-metadata';

import { resolve } from 'path';
import { readFileSync } from 'fs';
import { parse as parseDotenv } from 'dotenv';

const envFiles: string[] = [resolve(__dirname, '../../.env'), resolve(__dirname, '../../.env.local')];

const protectedKeys: Record<string, boolean> = {};
const knownKeys: Record<string, boolean> = {};

const { length: filesCount } = envFiles;

for (let i = 0; i < filesCount; i += 1) {
  const file = envFiles[i];

  let content: string;

  try {
    content = readFileSync(file, 'utf-8');
  } catch (error) {
    // eslint-disable-next-line no-continue
    continue;
  }

  const rawEnv = parseDotenv(content);

  const keys = Object.keys(rawEnv);
  const { length: keysCount } = keys;

  for (let j = 0; j < keysCount; j += 1) {
    const key = keys[j];

    const isUnknown = !knownKeys[key];
    knownKeys[key] = true;

    if (isUnknown) {
      protectedKeys[key] = typeof process.env[key] !== 'undefined';
    }

    const isProtected = protectedKeys[key];

    if (isProtected) {
      // eslint-disable-next-line no-continue
      continue;
    }

    const value = rawEnv[key];

    if (value) {
      process.env[key] = value;
    }
  }
}
