import { Arg, Int, Mutation, Query, Resolver } from 'type-graphql';
import { EdgeSyncService, OTAMetaDataService } from '../services';
import { logger } from '../helpers';
import { MongoDBAdapter } from '../adapters';
import {
  EdgeSyncInput,
  FilterInput,
  OtaMetadataResponse,
  PaginationInput,
  TargetVersionResponse,
  VersionPaginationInput,
  EdgeSyncConfig,
  SyncEdgesResponse,
  CurrentTargetVersionsPagination,
} from '../models';

@Resolver()
export class OtaMetaDataResolver {
  private readonly otaMetaDataService: OTAMetaDataService;

  private readonly mongoDb: MongoDBAdapter;

  constructor() {
    this.otaMetaDataService = new OTAMetaDataService();
    this.mongoDb = new MongoDBAdapter();
  }

  private async init() {
    await this.mongoDb.connect();
  }

  @Query(() => OtaMetadataResponse)
  async getOtaMetaData(
    @Arg('pagination') { page, limit }: PaginationInput,
    @Arg('filters') filters: FilterInput,
  ): Promise<OtaMetadataResponse> {
    try {
      await this.init();

      if (page <= 0 || limit <= 0) {
        throw new Error('Page and limit must be positive integers.');
      }

      logger.debug(`Filters: ${JSON.stringify(filters)}`);

      return await this.otaMetaDataService.getOTAMetaData(filters, page, limit);
    } catch (err: any) {
      return {
        data: [],
        totalRecords: 0,
        currentPage: page,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false,
        error: {
          message: err.message || 'Failed to fetch OTA metadata.',
        },
      };
    }
  }

  @Query(() => TargetVersionResponse)
  async getTargetVersions(
    @Arg('input') input: VersionPaginationInput,
    @Arg('versionFilter', { nullable: true }) versionFilter?: string,
  ): Promise<TargetVersionResponse> {
    try {
      const { targetDirectory } = input;
      if (!targetDirectory) {
        throw new Error('Target name is required to fetch versions.');
      }

      const { versionDetails } = await this.otaMetaDataService.getTargetVersionsFromS3({
        targetDirectory,
        versionFilter,
      });

      return {
        versionDetails,
      };
    } catch (err: any) {
      return {
        versionDetails: [],
        error: {
          message: err.message || 'Failed to fetch target versions.',
        },
      };
    }
  }

  @Mutation(() => SyncEdgesResponse)
  async syncEdgesToMongo(@Arg('criteria') criteria: EdgeSyncInput): Promise<any> {
    try {
      await this.init();

      const { edgeModels, batchSize, maxRetries, includeAssets } = criteria;

      // Validate input
      if (!edgeModels || edgeModels.length === 0) {
        throw new Error('Edge models array cannot be empty');
      }

      if (batchSize <= 0 || batchSize > 100) {
        throw new Error('Batch size must be a positive integer or less than or equal to 100');
      }

      if (maxRetries < 0 || maxRetries > 3) {
        throw new Error('Max retries cannot be negative or less than 3.');
      }

      const config: EdgeSyncConfig = {
        filters: {
          isDeleted: { equalTo: false },
          domain: {
            equalTo: 'edge',
          },
          model: {
            oneOf: edgeModels,
          },
        },
        batchSize,
        maxRetries,
        includeAssets,
      };

      const edgeSyncService = new EdgeSyncService(config);

      try {
        const startTime = Date.now();
        const result = await edgeSyncService.syncAllEdgesToMongo();
        const duration = Date.now() - startTime;

        return {
          message: 'Sync process completed.',
          syncedCount: result?.syncedCount,
          failedCount: result?.failedCount,
          durationMs: duration,
          successfulEdges: result?.details.successful.length,
          failedEdges: result?.details.failed.length,
        };
      } catch (error: any) {
        return {
          message: error.message || 'Edge sync failed',
          error: error.message,
          syncedCount: 0,
          failedCount: 0,
          durationMs: 0,
          successfulEdges: 0,
          failedEdges: 0,
        };
      }
    } catch (error: any) {
      return {
        message: error.message || 'Invalid input for edge sync',
        error: error.message,
        syncedCount: 0,
        failedCount: 0,
        durationMs: 0,
        successfulEdges: 0,
        failedEdges: 0,
      };
    }
  }

  @Query(() => CurrentTargetVersionsPagination)
  async getCurrenTargetVersions(
    @Arg('search', { nullable: true }) search: string,
    @Arg('page', () => Int, { defaultValue: 1 }) page: number,
    @Arg('limit', () => Int, { defaultValue: 10 }) limit: number,
  ): Promise<CurrentTargetVersionsPagination> {
    await this.init();
    return this.otaMetaDataService.getCurrenTargetVersions({ search, page, limit });
  }
}
