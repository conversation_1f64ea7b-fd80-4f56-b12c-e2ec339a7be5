/* eslint-disable max-classes-per-file */
import * as GQL from 'type-graphql';
import { TargetDirectories, TargetFirmwareNames } from '../../enum';

@GQL.InputType()
export class UploadCjcFirmwareCriteria {
  @GQL.Field(() => TargetFirmwareNames, { nullable: false })
  firmwareName!: TargetFirmwareNames;

  @GQL.Field({ nullable: false })
  version!: string;

  @GQL.Field(() => TargetDirectories)
  targetDirectory!: TargetDirectories;

  @GQL.Field({ defaultValue: false })
  isOverwrite!: boolean;

  @GQL.Field({ nullable: true })
  updatedBy: string;
}

@GQL.ObjectType()
export class UploadCjcFirmwareResponse {
  @GQL.Field()
  success!: boolean;

  @GQL.Field()
  message!: string;

  @GQL.Field({ nullable: true })
  uploadUrl?: string;
}
