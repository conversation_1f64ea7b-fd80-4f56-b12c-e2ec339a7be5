{"extends": "@tsconfig/node20/tsconfig.json", "compileOnSave": true, "compilerOptions": {"moduleResolution": "node", "module": "CommonJS", "target": "es2022", "lib": ["es2022", "esnext.asynciterable"], "declaration": true, "strict": true, "esModuleInterop": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "sourceMap": true, "inlineSources": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "typeRoots": ["./node_modules/@types"], "types": ["node", "jest"], "outDir": "./dist", "baseUrl": "src", "paths": {}}, "include": ["infra/**/*.js", "infra/**/*.ts", "src/**/*.js", "src/**/*.ts"], "exclude": ["node_modules"]}