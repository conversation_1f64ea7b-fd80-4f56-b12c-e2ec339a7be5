import { JobStatus } from '@aws-sdk/client-iot';

const JOB_ID = 'cjc-TCBGWC2MY40789982-19700101000000';
const MOCKED_DATE_STRING = '2025-01-22T04:00:18.513Z';
const MOCKED_LASTUPDATED_DATE_STRING = '2025-01-21T04:00:18.513Z';
const MOCKED_DATE = new Date(MOCKED_DATE_STRING);
const MOCKED_LASTUPDATED_DATE = new Date(MOCKED_LASTUPDATED_DATE_STRING);

export const mockDescribeJobExecutionCommandResponse = {
  jobId: JOB_ID,
  executionNumber: 1,
  status: JobStatus.IN_PROGRESS,
  statusDetails: {
    firmware1: JobStatus.IN_PROGRESS,
  },
  queuedAt: MOCKED_DATE,
  startedAt: MOCKED_DATE,
  lastUpdatedAt: MOCKED_LASTUPDATED_DATE,
};

export const mockDescribeJobCommandResponse = {
  jobId: JOB_ID,
  completedAt: MOCKED_DATE,
  createdAt: MOCKED_DATE,
  lastUpdatedAt: MOCKED_LASTUPDATED_DATE,
  status: JobStatus.COMPLETED,
  targets: ['target1'],
};

export const mockDescribeJobExecutionServiceResponse = [
  {
    jobId: JOB_ID,
    executionNumber: 1,
    status: JobStatus.IN_PROGRESS,
    progress: [{ firmwareName: 'firmware1', status: JobStatus.IN_PROGRESS }],
    queuedAt: MOCKED_DATE_STRING,
    startedAt: MOCKED_DATE_STRING,
    lastUpdatedAt: MOCKED_LASTUPDATED_DATE_STRING,
  },
];

export const mockDescribeJobServiceResponse = [
  {
    jobId: JOB_ID,
    completedAt: MOCKED_DATE_STRING,
    createdAt: MOCKED_DATE_STRING,
    lastUpdatedAt: MOCKED_LASTUPDATED_DATE_STRING,
    status: JobStatus.COMPLETED,
    targets: ['target1'],
  },
];

export const mockJobIds = { jobIds: [JOB_ID] };

export const mockCredentials = {
  accessKeyId: 'mockAccessKey',
  secretAccessKey: 'mockSecretKey',
  sessionToken: 'mockSessionToken',
  region: 'mockRegion',
};

export const mockContext = {
  event: { headers: { 'x-amzn-trace-id': 'mock-trace-id' } },
  context: { awsRequestId: 'mock-request-id' },
};
