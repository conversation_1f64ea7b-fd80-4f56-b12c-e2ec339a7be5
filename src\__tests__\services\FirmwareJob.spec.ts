import { DescribeJobCommandOutput, DescribeJobExecutionCommandOutput, IoTClient } from '@aws-sdk/client-iot';
import { FirmwareJobService } from '../../services/FirmwareJobService';
import {
  mockDescribeJobCommandResponse,
  mockDescribeJobExecutionCommandResponse,
  mockDescribeJobExecutionServiceResponse,
  mockDescribeJobServiceResponse,
  mockJobIds,
} from '../mocks/mocks';
import { IotService } from '../../services/IotService';
import { invalidJobIdReponse, isValidJobId, verifyJobIdAndExtractThingName } from '../../helpers/utilFunction';

jest.mock('@aws-sdk/client-iot');
jest.mock('../../helpers/utilFunction');

const mockedIoTClient = IoTClient as jest.MockedClass<typeof IoTClient>;
const mockedIotService = IotService as jest.MockedClass<typeof IotService>;
const mockedInvalidJobIdReponse = invalidJobIdReponse as jest.Mock;
const mockedIsValidJobId = isValidJobId as jest.Mock;
const mockedVerifyJobIdAndExtractThingName = verifyJobIdAndExtractThingName as jest.Mock;

describe('FirmwareJobService', () => {
  let service: FirmwareJobService;

  beforeEach(() => {
    service = new FirmwareJobService();
    mockedIoTClient.prototype.send = jest.fn();
    mockedIotService.prototype.sendIotJob = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('describeFirmwareJob', () => {
    it('should return firmware job details for valid job IDs', async () => {
      const mockJobResponse: Omit<DescribeJobCommandOutput, '$metadata'> = {
        job: mockDescribeJobCommandResponse,
      };
      mockedIotService.prototype.sendIotJob.mockResolvedValueOnce(mockJobResponse as DescribeJobCommandOutput);
      mockedIsValidJobId.mockReturnValueOnce(true);

      const result = await service.describeFirmwareJob(mockJobIds.jobIds);

      expect(result).toEqual(mockDescribeJobServiceResponse);
      expect(mockedIotService.prototype.sendIotJob).toHaveBeenCalledTimes(1);
    });
    it('should return invalid job response for invalid job IDs', async () => {
      mockedInvalidJobIdReponse.mockReturnValueOnce({ jobId: 'invalidJobId1', status: 'JOB_ID_NOT_FOUND' });

      const result = await service.describeFirmwareJob(mockJobIds.jobIds);

      expect(result).toEqual([{ jobId: 'invalidJobId1', status: 'JOB_ID_NOT_FOUND' }]);
      expect(mockedIotService.prototype.sendIotJob).not.toHaveBeenCalled();
    });

    it('should return invalid job response when an error occurs', async () => {
      mockedIotService.prototype.sendIotJob.mockRejectedValue(new Error('IoT Error'));
      mockedInvalidJobIdReponse.mockReturnValueOnce({ jobId: 'invalidJobId1', status: 'JOB_ID_NOT_FOUND' });

      mockedIsValidJobId.mockReturnValueOnce(true);
      const result = await service.describeFirmwareJob(mockJobIds.jobIds);

      expect(result).toEqual([{ jobId: 'invalidJobId1', status: 'JOB_ID_NOT_FOUND' }]);
      expect(mockedIotService.prototype.sendIotJob).toHaveBeenCalledTimes(1);
    });
  });
  describe('describeFirmwareJobExecution', () => {
    it('should return firmware job execution details for valid job IDs', async () => {
      const mockExecutionResponse: any = {
        execution: mockDescribeJobExecutionCommandResponse,
      };

      mockedIotService.prototype.sendIotJob.mockResolvedValueOnce(
        mockExecutionResponse as DescribeJobExecutionCommandOutput,
      );
      mockedIsValidJobId.mockReturnValueOnce(true);
      mockedVerifyJobIdAndExtractThingName.mockReturnValueOnce('cjc-TCBGWC2MY40789982');

      const result = await service.describeFirmwareJobExecution(mockJobIds.jobIds);

      expect(result).toEqual(mockDescribeJobExecutionServiceResponse);
      expect(mockedIotService.prototype.sendIotJob).toHaveBeenCalledTimes(1);
    });

    it('should return invalid job response for invalid job IDs', async () => {
      mockedInvalidJobIdReponse.mockReturnValueOnce({ jobId: 'invalidJobId1', status: 'JOB_ID_NOT_FOUND' });

      const result = await service.describeFirmwareJobExecution(mockJobIds.jobIds);

      expect(result).toEqual([{ jobId: 'invalidJobId1', status: 'JOB_ID_NOT_FOUND' }]);
      expect(mockedIotService.prototype.sendIotJob).not.toHaveBeenCalled();
    });

    it('should handle errors during job execution fetch', async () => {
      mockedIotService.prototype.sendIotJob.mockRejectedValueOnce(new Error('IoT Error'));
      mockedInvalidJobIdReponse.mockReturnValueOnce({ jobId: 'invalidJobId1', status: 'JOB_ID_NOT_FOUND' });

      mockedVerifyJobIdAndExtractThingName.mockReturnValueOnce(true);

      const result = await service.describeFirmwareJobExecution(mockJobIds.jobIds);

      expect(result).toEqual([{ jobId: 'invalidJobId1', status: 'JOB_ID_NOT_FOUND' }]);
      expect(mockedIotService.prototype.sendIotJob).toHaveBeenCalledTimes(1);
    });
  });
});
