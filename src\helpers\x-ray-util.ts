import * as AWSXRay from 'aws-xray-sdk-core';

import { logger } from './logger';

export function getTraceHeader(traceId?: string): Map<string, string> {
  const headers: Map<string, string> = new Map<string, string>();
  const headerAsString: string = traceId ?? process.env.X_AMZN_TRACE_ID ?? '';

  headerAsString.split(';').forEach((rootElement) => {
    const headerElements = rootElement.split('=');
    headers.set(headerElements[0], headerElements[1]);
  });
  return headers;
}

export function initialize(serviceName: string, fedGWTraceId?: string): AWSXRay.Segment {
  logger.info(`federated GW trace Id::${fedGWTraceId}`);
  const traceHeader: Map<string, string> = getTraceHeader(fedGWTraceId);
  const rootSegment: AWSXRay.Segment = new AWSXRay.Segment(
    serviceName,
    traceHeader.get('Root'),
    traceHeader.get('Parent'),
  );
  AWSXRay.setSegment(rootSegment);
  logger.info(`initialized rootSegment::${rootSegment?.trace_id}`);
  return rootSegment;
}

export function handleErrors(error: any, rootSegment: AWSXRay.Segment, awsRequestId: string): void {
  rootSegment.addMetadata('Lambda Request Id', awsRequestId);
  rootSegment.addError(JSON.stringify(error));
  if (error) {
    rootSegment.addErrorFlag();
  }
  rootSegment.close();
}
