import mongoose, { Schema, models } from 'mongoose';
import { EventInDb } from '../models/interfaces/LogEvent';

const properties = new Schema({
  key: String,
  value: String,
});

const EventSchema = new Schema({
  id: { type: String },
  timestamp: { type: Number },
  edgeId: { type: String, allowNull: false },
  edgeSN: { type: String, allowNull: true, index: true },
  properties: [properties],
  assetSN: { type: String, allowNull: true },
  assetName: { type: String, allowNull: true },
  assetId: { type: String, allowNull: true },
  name: { type: String },
  description: { type: String },
});

export const EventModel = models.Event || mongoose.model<EventInDb>('Event', EventSchema, 'Event');
