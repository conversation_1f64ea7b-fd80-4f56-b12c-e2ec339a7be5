import { FIRMWAREJOBSTATUS } from '../enum/CjcFirmawareEnum';
import { InvalidJobId } from '../models';
import { logger } from './logger';

export const isValidJobId = (jobId: string): boolean => {
  const jobIdPattern = /^[a-zA-Z0-9:_-]+-\d+$/;
  if (!jobIdPattern.test(jobId)) logger.error(`Invalid JobId: ${jobId}`);
  return jobIdPattern.test(jobId);
};

export const verifyJobIdAndExtractThingName = (jobId: string): string => {
  return isValidJobId(jobId) ? jobId.substring(0, jobId.lastIndexOf('-')) : '';
};

export const invalidJobIdReponse = (jobId: string): InvalidJobId => {
  return {
    jobId,
    status: FIRMWAREJOBSTATUS.JOBIDNOTFOUND,
  };
};

export const createCreateJobErrorMessage = (invalidThings = [], failedJobIds = []): string => {
  let errorMessage = '';
  if (invalidThings.length > 0) {
    errorMessage += `Seems like these things ${invalidThings.join(',')} is invalid.`;
  }
  if (failedJobIds.length > 0) {
    errorMessage += `Unable to create job for things ${failedJobIds.join(',')}.`;
  }
  return errorMessage;
};

export const getFormattedDateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${year}${month}${day}${hours}${minutes}${seconds}`;
};
