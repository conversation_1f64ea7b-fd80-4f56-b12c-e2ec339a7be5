import { SQSEvent } from 'aws-lambda';
import { handler } from '../../lambdas/OTAJobStatusSyncLambda';
import { IotService } from '../../services/IotService';
import { EdgeSyncService } from '../../services/EdgeSyncService';
import { MongoDBAdapter } from '../../adapters';
import { logger } from '../../helpers';

jest.mock('../../services/IotService');
jest.mock('../../services/EdgeSyncService');
jest.mock('../../adapters');
jest.mock('../../helpers');

const mockIotService = {
  getJobStatusAndTargetVersions: jest.fn(),
};

const mockEdgeSyncService = {
  updateJobStatusAndVersion: jest.fn(),
};

const mockMongoDBAdapter = {
  connect: jest.fn(),
};

const JOB_CREATE_ERROR_MESSAGE = 'Error while updating the job status';
const THING_ARN = 'arn:aws:iot:us-east-1:123456789012:thing/cjc-123456';
const TIMESTAMP = '2025-08-25T12:38:02.902Z';
const AMAZON_ROOT_CA_VERSION = '1.00.12.01';

(IotService as unknown as jest.Mock).mockImplementation(() => mockIotService);
(EdgeSyncService as unknown as jest.Mock).mockImplementation(() => mockEdgeSyncService);
(MongoDBAdapter as unknown as jest.Mock).mockImplementation(() => mockMongoDBAdapter);

describe('OTAJobStatusSyncLambda Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockMongoDBAdapter.connect.mockResolvedValue(undefined);
  });

  const createMockEvent = (payload: any): SQSEvent => {
    const base64Payload = Buffer.from(JSON.stringify(payload)).toString('base64');
    return {
      Records: [
        {
          body: base64Payload,
          messageId: '1',
          receiptHandle: '',
          attributes: {
            ApproximateReceiveCount: '',
            SentTimestamp: '',
            SenderId: '',
            ApproximateFirstReceiveTimestamp: '',
          },
          messageAttributes: {},
          md5OfBody: '',
          eventSource: '',
          eventSourceARN: '',
          awsRegion: '',
        },
      ],
    };
  };

  it('should process a valid SQS event for CJC thing', async () => {
    const payload = {
      jobId: 'job-123',
      status: 'COMPLETED',
      timestamp: TIMESTAMP,
      thingArn: THING_ARN,
    };

    const mockEvent = createMockEvent(payload);

    mockIotService.getJobStatusAndTargetVersions.mockResolvedValue([
      { targetName: 'AmazonRootCA1', version: AMAZON_ROOT_CA_VERSION },
    ]);

    await handler(mockEvent);

    expect(mockMongoDBAdapter.connect).toHaveBeenCalledTimes(1);
    expect(mockIotService.getJobStatusAndTargetVersions).toHaveBeenCalledWith('job-123');
    expect(mockEdgeSyncService.updateJobStatusAndVersion).toHaveBeenCalledWith('123456', {
      jobId: 'job-123',
      jobStatus: 'COMPLETED',
      completedAt: TIMESTAMP,
      targets: [{ targetName: 'AmazonRootCA1', version: AMAZON_ROOT_CA_VERSION }],
    });
  });

  it('should skip non-CJC things and return early', async () => {
    const payload = {
      jobId: 'job-123',
      status: 'COMPLETED',
      timestamp: TIMESTAMP,
      thingArn: 'arn:aws:iot:us-east-1:123456789012:thing/ahp-123456',
    };

    const mockEvent = createMockEvent(payload);

    await handler(mockEvent);

    expect(mockMongoDBAdapter.connect).toHaveBeenCalledTimes(1);
    expect(logger.warn).toHaveBeenCalledWith(
      'Skippng.... Non CJC thing found. Current only CJC thing event are processed.',
    );
    expect(mockIotService.getJobStatusAndTargetVersions).not.toHaveBeenCalled();
    expect(mockEdgeSyncService.updateJobStatusAndVersion).not.toHaveBeenCalled();
  });

  it('should handle case when targetVersions is null/undefined', async () => {
    const payload = {
      jobId: 'job-123',
      status: 'COMPLETED',
      timestamp: TIMESTAMP,
      thingArn: THING_ARN,
    };

    const mockEvent = createMockEvent(payload);

    mockIotService.getJobStatusAndTargetVersions.mockResolvedValue(null);

    await handler(mockEvent);

    expect(mockIotService.getJobStatusAndTargetVersions).toHaveBeenCalledWith('job-123');
    expect(logger.error).toHaveBeenCalledWith('Invalid target Version found, skipping update. targetVersions::: null');
    expect(mockEdgeSyncService.updateJobStatusAndVersion).toHaveBeenCalledWith('123456', {
      jobId: 'job-123',
      jobStatus: 'COMPLETED',
      completedAt: TIMESTAMP,
    });
  });

  it('should handle case when status is null/undefined', async () => {
    const payload = {
      jobId: 'job-123',
      status: null,
      timestamp: TIMESTAMP,
      thingArn: THING_ARN,
    };

    const mockEvent = createMockEvent(payload);

    mockIotService.getJobStatusAndTargetVersions.mockResolvedValue([
      { targetName: 'AmazonRootCA1', version: AMAZON_ROOT_CA_VERSION },
    ]);

    await handler(mockEvent);

    expect(mockEdgeSyncService.updateJobStatusAndVersion).toHaveBeenCalledWith('123456', {
      jobId: 'job-123',
      jobStatus: null,
      completedAt: TIMESTAMP,
      targets: [{ targetName: 'AmazonRootCA1', version: AMAZON_ROOT_CA_VERSION }],
    });
  });

  it('should log error if body is not valid base64', async () => {
    const EMPTY_STRING = '';
    const eventWithInvalidBody = {
      Records: [
        {
          body: 'not-a-valid-base64!@#$%',
          messageId: '1',
          receiptHandle: EMPTY_STRING,
          attributes: {
            ApproximateReceiveCount: EMPTY_STRING,
            SentTimestamp: EMPTY_STRING,
            SenderId: EMPTY_STRING,
            ApproximateFirstReceiveTimestamp: EMPTY_STRING,
          },
          messageAttributes: {},
          md5OfBody: EMPTY_STRING,
          eventSource: EMPTY_STRING,
          eventSourceARN: EMPTY_STRING,
          awsRegion: EMPTY_STRING,
        },
      ],
    };

    await handler(eventWithInvalidBody as SQSEvent);

    expect(logger.error).toHaveBeenCalledWith(JOB_CREATE_ERROR_MESSAGE, expect.any(String));
    expect(mockEdgeSyncService.updateJobStatusAndVersion).not.toHaveBeenCalled();
  });

  it('should log error if JSON is malformed', async () => {
    const badJson = Buffer.from('{"invalidJson": }').toString('base64');
    const event = {
      Records: [
        {
          body: badJson,
          messageId: '1',
          receiptHandle: '',
          attributes: {
            ApproximateReceiveCount: '',
            SentTimestamp: '',
            SenderId: '',
            ApproximateFirstReceiveTimestamp: '',
          },
          messageAttributes: {},
          md5OfBody: '',
          eventSource: '',
          eventSourceARN: '',
          awsRegion: '',
        },
      ],
    };

    await handler(event as SQSEvent);

    expect(logger.error).toHaveBeenCalledWith(JOB_CREATE_ERROR_MESSAGE, expect.any(String));
    expect(mockEdgeSyncService.updateJobStatusAndVersion).not.toHaveBeenCalled();
  });

  it('should handle error when thingArn split fails', async () => {
    const payload = {
      jobId: 'job-123',
      status: 'COMPLETED',
      timestamp: TIMESTAMP,
      thingArn: 'invalid-arn-format',
    };

    const mockEvent = createMockEvent(payload);

    await handler(mockEvent);

    expect(logger.error).toHaveBeenCalledWith(JOB_CREATE_ERROR_MESSAGE, expect.any(String));
    expect(mockEdgeSyncService.updateJobStatusAndVersion).not.toHaveBeenCalled();
  });

  it('should handle multiple records with mixed CJC and non-CJC things', async () => {
    const cjcPayload = {
      jobId: 'job-123',
      status: 'COMPLETED',
      timestamp: TIMESTAMP,
      thingArn: THING_ARN,
    };

    const nonCjcPayload = {
      jobId: 'job-456',
      status: 'COMPLETED',
      timestamp: TIMESTAMP,
      thingArn: 'arn:aws:iot:us-east-1:123456789012:thing/ahp-789012',
    };

    const multiEvent = {
      Records: [
        {
          body: Buffer.from(JSON.stringify(cjcPayload)).toString('base64'),
          messageId: '1',
          receiptHandle: '',
          attributes: {
            ApproximateReceiveCount: '',
            SentTimestamp: '',
            SenderId: '',
            ApproximateFirstReceiveTimestamp: '',
          },
          messageAttributes: {},
          md5OfBody: '',
          eventSource: '',
          eventSourceARN: '',
          awsRegion: '',
        },
        {
          body: Buffer.from(JSON.stringify(nonCjcPayload)).toString('base64'),
          messageId: '2',
          receiptHandle: '',
          attributes: {
            ApproximateReceiveCount: '',
            SentTimestamp: '',
            SenderId: '',
            ApproximateFirstReceiveTimestamp: '',
          },
          messageAttributes: {},
          md5OfBody: '',
          eventSource: '',
          eventSourceARN: '',
          awsRegion: '',
        },
      ],
    };

    mockIotService.getJobStatusAndTargetVersions.mockResolvedValue([
      { targetName: 'AmazonRootCA1', version: AMAZON_ROOT_CA_VERSION },
    ]);

    await handler(multiEvent as SQSEvent);

    // Should only process the CJC thing, then return early on non-CJC
    expect(mockIotService.getJobStatusAndTargetVersions).toHaveBeenCalledTimes(1);
    expect(mockIotService.getJobStatusAndTargetVersions).toHaveBeenCalledWith('job-123');
    expect(mockEdgeSyncService.updateJobStatusAndVersion).toHaveBeenCalledTimes(1);
    expect(logger.warn).toHaveBeenCalledWith(
      'Skippng.... Non CJC thing found. Current only CJC thing event are processed.',
    );
  });

  it('should handle service errors gracefully', async () => {
    const payload = {
      jobId: 'job-123',
      status: 'COMPLETED',
      timestamp: TIMESTAMP,
      thingArn: THING_ARN,
    };

    const mockEvent = createMockEvent(payload);

    mockIotService.getJobStatusAndTargetVersions.mockRejectedValue(new Error('IoT Service Error'));

    await handler(mockEvent);

    expect(logger.error).toHaveBeenCalledWith(JOB_CREATE_ERROR_MESSAGE, 'Error: IoT Service Error');
    expect(mockEdgeSyncService.updateJobStatusAndVersion).not.toHaveBeenCalled();
  });

  it('should handle EdgeSyncService errors gracefully', async () => {
    const payload = {
      jobId: 'job-123',
      status: 'COMPLETED',
      timestamp: TIMESTAMP,
      thingArn: THING_ARN,
    };

    const mockEvent = createMockEvent(payload);

    mockIotService.getJobStatusAndTargetVersions.mockResolvedValue([
      { targetName: 'AmazonRootCA1', version: AMAZON_ROOT_CA_VERSION },
    ]);
    mockEdgeSyncService.updateJobStatusAndVersion.mockRejectedValue(new Error('EdgeSync Service Error'));

    await handler(mockEvent);

    expect(mockIotService.getJobStatusAndTargetVersions).toHaveBeenCalledWith('job-123');
    expect(mockEdgeSyncService.updateJobStatusAndVersion).toHaveBeenCalledWith('123456', {
      jobId: 'job-123',
      jobStatus: 'COMPLETED',
      completedAt: TIMESTAMP,
      targets: [{ targetName: 'AmazonRootCA1', version: AMAZON_ROOT_CA_VERSION }],
    });
    expect(logger.error).toHaveBeenCalledWith(JOB_CREATE_ERROR_MESSAGE, 'Error: EdgeSync Service Error');
  });
});
