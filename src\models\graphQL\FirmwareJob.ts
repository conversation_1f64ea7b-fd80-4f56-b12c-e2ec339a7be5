import 'reflect-metadata';
import { ObjectType, Field, InputType, Int } from 'type-graphql';

@InputType()
export class DescribeFirmwareJobInput {
  @Field(() => [String])
  jobIds!: string[];
}

@ObjectType()
export class FirmwareJob {
  @Field()
  jobId: string;

  @Field(() => String, { nullable: true })
  completedAt?: string;

  @Field(() => String, { nullable: true })
  createdAt?: string;

  @Field(() => String, { nullable: true })
  lastUpdatedAt?: string;

  @Field(() => String)
  status?: string;

  @Field(() => [String], { nullable: true })
  targets?: string[];
}

@ObjectType()
export class FirmwareJobExecutionProgress {
  @Field()
  firmwareName!: string;

  @Field()
  status: string;
}

@ObjectType()
export class FirmwareJobExecution {
  @Field()
  jobId: string;

  @Field(() => Int, { nullable: true })
  executionNumber?: number;

  @Field(() => String, { nullable: true })
  lastUpdatedAt?: string;

  @Field(() => String, { nullable: true })
  queuedAt?: string;

  @Field(() => String, { nullable: true })
  startedAt?: string;

  @Field(() => String)
  status?: string;

  @Field(() => [FirmwareJobExecutionProgress], { nullable: true })
  progress?: FirmwareJobExecutionProgress[];
}
@ObjectType()
export class DescribeJobOutputError {
  @Field({ nullable: false })
  message: string;
}

@ObjectType()
export class FirmwareJobResponse {
  @Field(() => [FirmwareJob])
  data: FirmwareJob[];

  @Field(() => DescribeJobOutputError, { nullable: true })
  error?: DescribeJobOutputError;
}

@ObjectType()
export class FirmwareJobExecutionResponse {
  @Field(() => [FirmwareJobExecution])
  data: FirmwareJobExecution[];

  @Field(() => DescribeJobOutputError, { nullable: true })
  error?: DescribeJobOutputError;
}
