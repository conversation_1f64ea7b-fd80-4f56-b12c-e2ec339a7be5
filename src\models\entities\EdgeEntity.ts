import * as SDK from '@carrier-io/backend-lib-node-sdk';
import { EdgeEntityClass } from '@carrier-io/io-lib-node-sdk-models';
import { AssetEntity } from './AssetEntity';

@SDK.entityName('edge')
export class EdgeEntity extends EdgeEntityClass {
  @SDK.entityField(SDK.NodeFieldScope.custom)
  edgeId?: string;

  @SDK.entityField(SDK.NodeFieldScope.custom)
  mac?: string;

  @SDK.entityField(SDK.NodeFieldScope.custom)
  serialNumber?: string;

  @SDK.entityField(SDK.NodeFieldScope.custom)
  firmware?: string;

  @SDK.entityField(SDK.NodeFieldScope.custom)
  model?: string;

  @SDK.entityField(SDK.NodeFieldScope.custom)
  imei?: string;

  @SDK.entityField(SDK.NodeFieldScope.custom)
  lteVersion?: string;

  @SDK.entityField(SDK.NodeFieldScope.custom)
  esp32Version?: string;

  @SDK.entityField(SDK.NodeFieldScope.custom)
  rx651Version?: string;

  @SDK.entityField(SDK.NodeFieldScope.custom)
  isOnline?: boolean;

  @SDK.entityField(SDK.NodeFieldScope.custom)
  isEdgeReplaced?: boolean;

  // @ts-ignore
  assets?: SDK.RelationToMany<AssetEntity>;
}
