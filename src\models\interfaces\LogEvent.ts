import { Document, Types } from 'mongoose';

export interface Properties {
  key: string;
  value: ITargetVersions[] | string;
}

export interface ITargetVersions {
  firmwareName: string;
  version: string;
}

export interface EventLog {
  timestamp: number;
  edgeSN: string;
  edgeId: string;
  assetSN?: string;
  assetName?: string;
  assetId?: string;
  name?: string;
  properties: Properties[];
  description: string | '';
}

export type IEventLog = Partial<EventLog>;

export interface EventInDb extends Document<Types.ObjectId>, EventLog {}
