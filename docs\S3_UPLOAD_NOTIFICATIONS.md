# S3 Upload Notifications Setup

This document explains how to set up S3 upload notifications for automatic firmware signing after upload.

## Overview

When firmware files are uploaded to S3 buckets via presigned URLs, the system can automatically:
1. Detect the upload via S3 event notifications
2. Trigger a Lambda function to process the upload
3. Start an AWS Signer job to sign the firmware
4. Store the signed firmware for OTA distribution in /signed folder

## Architecture

```
Frontend Upload → S3 Bucket → S3 Event → Lambda Function → AWS Signer → Signed Firmware
```

## Components Added

### 1. Lambda Function
- **File**: `src/lambdas/FirmwareUploadNotificationLambda.ts`
- **Purpose**: Handles S3 upload events and triggers signing jobs
- **Triggers**: S3 ObjectCreated events for `.bin` files

### 2. CDK Infrastructure Updates
- **File**: `infra/stack/CjcOtaStack.ts`
- **Added**: Upload notification Lambda function
- **Permissions**: S3 access, AWS Signer access, Lambda invoke permissions

### 3. S3 Notification Configurations or add it manually
- **Files**: 
  - `infra/s3-notifications/cjc-bucket-notification.json`
  - `infra/s3-notifications/ahp-bucket-notification.json`
- **Purpose**: Template configurations for S3 bucket notifications

### 4. Setup Script
- **File**: `scripts/setup-s3-notifications.sh`
- **Purpose**: Automated script to configure S3 bucket notifications

## Deployment Steps

### 1. Deploy CDK Stack
```bash
# Deploy the updated CDK stack
npm run cdk:deploy

# Or for specific stage
npm run cdk:deploy -- --context stage=dev
```

### 2. Configure S3 Notifications
```bash
# Make the script executable
chmod +x scripts/setup-s3-notifications.sh

# Run the setup script
./scripts/setup-s3-notifications.sh dev

# Or manually configure using AWS CLI
aws s3api put-bucket-notification-configuration \
  --bucket cjc-backup-files-bucket-dev \
  --notification-configuration file://infra/s3-notifications/cjc-bucket-notification.json
```

### 3. Set Environment Variables
Ensure these environment variables are set in your Lambda:
# creating 3 profiles for 3 different types of firmware or integrate DB to get the profiles
```
AWS_SIGNER_PROFILE_NAME=Test_ESP32FirmwareProfile
AWS_REGION=us-east-1
```

## Configuration

### Signing Rules
The Lambda function will automatically sign files that match these criteria:

**File Patterns:**
- `*.bin` files (binary firmware)
- Files containing `ESP32_firmware`
- Files containing `ESP32_filesystem`
- Files matching `RX651_CHL.mot`

### S3 Event Filters

**CJC Bucket (`cjc-backup-files-bucket-{stage}`):**
- Prefix: `GatewayDocs/OTA/`
- Suffix: `.bin`


## Testing

### 1. Upload Test File
```bash
# Upload a test firmware file
aws s3 cp test-firmware.bin s3://cjc-backup-files-bucket-dev/GatewayDocs/OTA/ESP32/v1.0.0/ESP32_firmware.bin
```

### 2. Check Lambda Logs
```bash
# View Lambda function logs
aws logs tail /aws/lambda/ahp-cjc-ota-job-UploadNotificationLambda-dev --follow
```

### 3. Verify Signing Job
```bash
# List recent signing jobs
aws signer list-signing-jobs --max-results 10

# Check specific job status
aws signer describe-signing-job --job-id <job-id>
```

## Troubleshooting

### Common Issues

1. **Lambda not triggered**
   - Check S3 bucket notification configuration
   - Verify Lambda permissions
   - Check S3 event filters

2. **Signing job fails**
   - Verify signing profile exists and is active
   - Check certificate validity
   - Ensure proper IAM permissions for AWS Signer

3. **Permission errors**
   - Verify Lambda execution role has required permissions
   - Check S3 bucket policies
   - Ensure cross-account access if needed

### Debug Commands
```bash
# Check bucket notification configuration
aws s3api get-bucket-notification-configuration --bucket cjc-backup-files-bucket-dev

# Test Lambda function directly
aws lambda invoke \
  --function-name ahp-cjc-ota-job-UploadNotificationLambda-dev \
  --payload file://test-s3-event.json \
  response.json

# List signing profiles
aws signer list-signing-profiles
```

## Security Considerations

1. **Least Privilege**: Lambda has minimal required permissions
2. **Bucket Policies**: Ensure S3 buckets have appropriate access controls
3. **Signing Certificates**: Regularly rotate signing certificates
4. **Monitoring**: Enable CloudTrail for audit logging

## Future Enhancements

1. **Database Integration**: Store signing job metadata
2. **Notification System**: Send alerts on signing completion/failure
3. **Batch Processing**: Handle multiple files in single invocation
4. **Retry Logic**: Implement exponential backoff for failed jobs
5. **File Validation**: Add firmware validation before signing
