import {
  TargetDirectories,
  TargetFirmwareNames,
  TargetFirmwareWithExtentions,
  TargetUpdateOperations,
} from '../../enum';
import { JobDocumentBuilder } from '../../helpers/jobDocumentBuilder';
import { FileInfo, LTEModuleFileInfo } from '../../models';
import { S3Service } from '../../services/S3Service';

jest.mock('aws-sdk', () => {
  const S3 = jest.fn(() => ({
    headObject: jest.fn().mockReturnThis(),
    promise: jest.fn(),
    getSignedUrl: jest.fn(),
  }));
  return { S3 };
});

jest.mock('../../services/S3Service');
const mockTestBinFileName = 'test_file.bin';
describe('JobDocumentBuilder', () => {
  let jobDocumentBuilder: JobDocumentBuilder;
  let s3Service: jest.Mocked<S3Service>;

  const mockFilePath = 'GatewayDocs/OTA/mockModuleName/mockVersion/mockFileName';

  beforeEach(() => {
    process.env.CJC_OTA_SERVICE_BUCKET_NAME = 'test-bucket';
    process.env.STAGE = 'dev';
    process.env.AWS_REGION = 'us-west-2';
    process.env.S3_URL_EXPIRATION_TIME = '3600';

    s3Service = new S3Service() as jest.Mocked<S3Service>;
    jobDocumentBuilder = new JobDocumentBuilder();
    (jobDocumentBuilder as any)._s3Service = s3Service;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('_checkJobFilesExistInS3', () => {
    it('should check if files exist in S3', async () => {
      const files: FileInfo[] = [
        { filename: 'test1.bin', version: '1.0.0', priority: 3, moduleName: 'bulksAll', url: 'hello' },
        { filename: 'test2.bin', version: '1.0.0', priority: 3, moduleName: 'bulksAll', url: 'hey' },
      ];

      s3Service.checkFileExistInS3.mockResolvedValue(true);

      await (jobDocumentBuilder as any)._checkJobFilesExistInS3(files);
      expect(s3Service.checkFileExistInS3).toHaveBeenCalledTimes(2);
      expect(s3Service.checkFileExistInS3).toHaveBeenCalledWith('GatewayDocs/OTA/bulksAll/1.0.0/test1.bin');
      expect(s3Service.checkFileExistInS3).toHaveBeenCalledWith('GatewayDocs/OTA/bulksAll/1.0.0/test2.bin');
    });

    it('should return true if the file exists in S3', async () => {
      s3Service.checkFileExistInS3.mockResolvedValue(true);

      const result = await s3Service.checkFileExistInS3(mockFilePath);
      expect(result).toBe(true);
    });
  });

  describe('_getFilesForTargetDirectory', () => {
    it('should return files for bulksAll target directory', async () => {
      const files: FileInfo[] = [
        { filename: 'test1.bin', version: '1.0.0', priority: 3, moduleName: 'bulksAll', url: 'hello' },
        { filename: 'test2.bin', version: '1.0.0', priority: 3, moduleName: 'bulksAll', url: 'hey' },
      ];

      (jobDocumentBuilder as any)._createFileInfo = jest
        .fn()
        .mockResolvedValueOnce(files[0])
        .mockResolvedValueOnce(files[1]);

      const targetVersions = [
        {
          firmwareName: TargetFirmwareWithExtentions.ESP32_firmware,
          version: '1.0',
        },
        {
          firmwareName: TargetFirmwareWithExtentions.ESP32_filesystem,
          version: '1.1',
        },
        {
          firmwareName: TargetFirmwareWithExtentions.RX651_CHL,
          version: '1.2',
        },
      ];

      const result = await (jobDocumentBuilder as any)._getFilesForTargetDirectory(
        TargetUpdateOperations.BulksAll,
        targetVersions,
      );
      expect(result).toEqual(files);
    });

    it('should return files for LTEmodule target directory', async () => {
      const lteModuleUrl = `https://test-bucket.s3.us-west-2.amazonaws.com/LTEmodule/1.0.0/lte_module.bin`;
      const files: LTEModuleFileInfo[] = [
        {
          url: lteModuleUrl,
          moduleName: TargetDirectories.LTEmodule,
        },
      ];

      const targetVersions = [
        {
          firmwareName: TargetFirmwareNames.LTEmodule,
          version: '1.0',
        },
      ];

      const result = await (jobDocumentBuilder as any)._getFilesForTargetDirectory(
        TargetDirectories.LTEmodule,
        targetVersions,
        lteModuleUrl,
      );
      expect(result).toEqual(files);
    });

    it('should throw an error for an unknown target directory', async () => {
      const targetVersions = [
        {
          firmwareName: TargetFirmwareNames.ESP32_firmware,
          version: '1.0',
        },
      ];
      await expect(
        (jobDocumentBuilder as any)._getFilesForTargetDirectory('unknown' as TargetUpdateOperations, targetVersions),
      ).rejects.toThrowError('Unknown target operation: unknown');
    });
  });

  describe('_buildJobDocument', () => {
    it('should build a job document for the specified target directory and version', async () => {
      const files = [
        {
          filename: TargetFirmwareWithExtentions.ESP32_firmware,
          url: 'https://test-bucket.s3.us-west-2.amazonaws.com/bulksAll/1.0.0/ESP32_firmware.bin',
          version: '1.0',
          priority: 3,
          moduleName: TargetUpdateOperations.BulksAll,
          firmwareName: TargetFirmwareNames.ESP32_firmware,
        },
        {
          filename: TargetFirmwareWithExtentions.ESP32_filesystem,
          url: 'https://test-bucket.s3.us-west-2.amazonaws.com/bulksAll/1.0.0/ESP32_filesystem.bin',
          version: '1.1',
          priority: 2,
          moduleName: TargetUpdateOperations.BulksAll,
          firmwareName: TargetFirmwareNames.ESP32_filesystem,
        },
        {
          filename: TargetFirmwareWithExtentions.RX651_CHL,
          url: 'https://test-bucket.s3.us-west-2.amazonaws.com/bulksAll/1.0.0/RX651_CHL.mot',
          version: '1.2',
          priority: 1,
          moduleName: TargetUpdateOperations.BulksAll,
          firmwareName: TargetFirmwareNames.RX651_CHL,
        },
      ];

      const targetVersions = [
        {
          firmwareName: TargetFirmwareNames.ESP32_firmware,
          version: '1.0',
        },
        {
          firmwareName: TargetFirmwareNames.ESP32_filesystem,
          version: '1.1',
        },
        {
          firmwareName: TargetFirmwareNames.RX651_CHL,
          version: '1.2',
        },
      ];

      const jobDocument = (jobDocumentBuilder as any)._buildJobDocument(
        TargetUpdateOperations.BulksAll,
        targetVersions,
        files,
      );
      expect(jobDocument).toBeDefined();
      expect(jobDocument.version).toBe('');
      expect(jobDocument.steps).toHaveLength(1);
      expect(jobDocument.steps[0].action.input.args).toHaveLength(1);
    });
  });

  describe('createJobDocument', () => {
    const ESP32_FIRMWARE_URL = 'https://test-bucket.s3.us-west-2.amazonaws.com/bulksAll/1.0.0/ESP32_firmware.bin';
    it('should build a job document for the specified target directory and version', async () => {
      const files = [
        {
          filename: TargetFirmwareWithExtentions.ESP32_firmware,
          url: ESP32_FIRMWARE_URL,
          version: '1.0',
          priority: 3,
          moduleName: TargetDirectories.ESP32_firmware,
          firmwareName: TargetFirmwareNames.ESP32_firmware,
        },
        {
          filename: TargetFirmwareWithExtentions.ESP32_filesystem,
          url: 'https://test-bucket.s3.us-west-2.amazonaws.com/bulksAll/1.0.0/ESP32_filesystem.bin',
          version: '1.1',
          priority: 2,
          moduleName: TargetDirectories.ESP32_filesystem,
          firmwareName: TargetFirmwareNames.ESP32_filesystem,
        },
        {
          filename: TargetFirmwareWithExtentions.RX651_CHL,
          url: 'https://test-bucket.s3.us-west-2.amazonaws.com/bulksAll/1.0.0/RX651_CHL.mot',
          version: '1.2',
          priority: 1,
          moduleName: TargetDirectories.RX651_CHL,
          firmwareName: TargetFirmwareNames.RX651_CHL,
        },
      ];

      const targetVersions = [
        {
          firmwareName: TargetFirmwareNames.ESP32_firmware,
          version: '1.0',
        },
        {
          firmwareName: TargetFirmwareNames.ESP32_filesystem,
          version: '1.1',
        },
        {
          firmwareName: TargetFirmwareNames.RX651_CHL,
          version: '1.2',
        },
      ];

      s3Service.checkFileExistInS3.mockResolvedValue(true);
      (jobDocumentBuilder as any)._getFilesForTargetDirectory = jest.fn().mockResolvedValue(files);

      const jobDocument = await jobDocumentBuilder.createJobDocument(TargetUpdateOperations.BulksAll, targetVersions);
      expect(jobDocument).toBeDefined();
      expect(jobDocument.version).toBe('');
      expect(jobDocument.steps).toHaveLength(1);
      expect(jobDocument.steps[0].action.input.args).toHaveLength(1);
    });

    it('should throw an error if LTE module URL is not provided for LTEmodule target directory', async () => {
      const targetVersion = [
        {
          firmwareName: TargetFirmwareNames.LTEmodule,
          version: '1.0.0',
        },
      ];

      await expect(
        jobDocumentBuilder.createJobDocument(TargetUpdateOperations.LTEmodule, targetVersion),
      ).rejects.toThrowError('LTE module URL must be provided when LTEmodule is included in target versions.');
    });

    it('should throw an error if S3 throws an unexpected error', async () => {
      const version = '1.0.0';
      const files: FileInfo[] = [
        {
          filename: TargetFirmwareWithExtentions.ESP32_firmware,
          url: ESP32_FIRMWARE_URL,
          version,
          priority: 3,
          moduleName: TargetDirectories.ESP32_firmware,
        },
      ];

      const targetVersions = [
        {
          firmwareName: TargetFirmwareNames.ESP32_firmware,
          version,
        },
      ];
      s3Service.checkFileExistInS3.mockRejectedValue(new Error('Unexpected error'));
      (jobDocumentBuilder as any)._getFilesForTargetDirectory = jest.fn().mockResolvedValue(files);

      await expect(
        jobDocumentBuilder.createJobDocument(TargetUpdateOperations.BulksAll, targetVersions),
      ).rejects.toThrowError('Error in createJobDocument: Unexpected error');
    });
  });

  describe('_createFileInfo', () => {
    it('should create file info correctly', async () => {
      const url = 'https://test-bucket-dev.s3.us-west-2.amazonaws.com/bulksAll/1.0.0/test_file.bin';
      s3Service.generatePresignedUrl.mockResolvedValue(url);

      const fileInfo = await (jobDocumentBuilder as any)._createFileInfo(
        TargetDirectories.RX651_CHL,
        '1.0.0',
        mockTestBinFileName,
        TargetFirmwareNames.RX651_CHL,
        3,
      );
      expect(fileInfo).toEqual({
        filename: mockTestBinFileName,
        url,
        version: '1.0.0',
        priority: 3,
        moduleName: TargetDirectories.RX651_CHL,
        firmwareName: TargetFirmwareNames.RX651_CHL,
      });
    });
  });

  describe('_generateFileUrl', () => {
    it('should generate the correct file URL', async () => {
      const sampleUrl = 'https://test-bucket-dev.s3.us-west-2.amazonaws.com/bulksAll/1.0.0/test_file.bin';
      s3Service.generatePresignedUrl.mockResolvedValue(sampleUrl);

      const result = await (jobDocumentBuilder as any)._generateFileUrl(
        TargetDirectories.ESP32_firmware,
        '1.0.0',
        mockTestBinFileName,
      );
      expect(result).toBe(sampleUrl);
    });
  });
});
