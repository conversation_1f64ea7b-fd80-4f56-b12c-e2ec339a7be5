import jwt from 'jsonwebtoken';

import { UploadCjcFirmwareCriteria, UploadCjcFirmwareResponse } from '../models/graphQL/UploadCJCFirmwareModel';
import { logger } from '../helpers/logger';
import { UserAuthJwt } from '../types/userAuthJwt';
import { BASE_S3_OTA_FILE_PATH } from '../models';
import { TargetFirmwareNames, TargetFirmwareWithExtentions } from '../enum';
import { S3Service } from './S3Service';

export class UploadCjcFirmwareService {
  private s3Service: S3Service;

  constructor() {
    this.s3Service = new S3Service();
  }

  async handleFirmwareUpload(
    criteria: UploadCjcFirmwareCriteria,
    authToken: string,
  ): Promise<UploadCjcFirmwareResponse> {
    const { firmwareName, version, targetDirectory, isOverwrite } = criteria;

    if (firmwareName.includes(TargetFirmwareNames.LTEmodule)) {
      throw new Error(`Upload feature is not available for ${TargetFirmwareNames.LTEmodule}`);
    }

    let { updatedBy } = criteria;
    if (!updatedBy && authToken) {
      try {
        const decodedToken = jwt.decode(authToken) as UserAuthJwt;
        if (decodedToken && decodedToken.sub) {
          updatedBy = decodedToken.sub;
        }
      } catch (error: any) {
        logger.error('Error decoding token or extracting email:', error);
        return {
          success: false,
          message: 'Failed to extract email from authorization token.',
        };
      }
    }

    const versionRegex = /^[0-9a-zA-Z]{1,4}\.[0-9a-zA-Z]{1,4}\.[0-9a-zA-Z]{1,4}(\.[0-9a-zA-Z]{1,4})?$/;
    if (!versionRegex.test(version)) {
      return {
        success: false,
        message: "Invalid version format. Only numeric values and '.' are allowed, max length 10.",
      };
    }

    const filePath = `${BASE_S3_OTA_FILE_PATH}${targetDirectory}/${version}/${
      TargetFirmwareWithExtentions[firmwareName as keyof typeof TargetFirmwareWithExtentions]
    }`;

    try {
      let fileExists = false;
      try {
        await this.s3Service.checkFileExistInS3(filePath);
        fileExists = true;
      } catch (error: any) {
        if (error.code !== 'NotFound') {
          logger.error('Error checking file existence:', error);
          fileExists = false;
        }
      }

      if (fileExists && !isOverwrite) {
        return {
          success: false,
          message: 'File already exists and isOverwrite was false.',
        };
      }

      const presignedUrl = await this.s3Service.generatePresignedUrl(filePath, 'putObject');

      return {
        success: true,
        message: 'Firmware upload is valid. Use the provided S3 URL to upload the firmware file.',
        uploadUrl: presignedUrl,
      };
    } catch (error: any) {
      logger.error('Error in firmware upload process:', error);
      return {
        success: false,
        message: 'Upload CJC Firmware Service::An error occurred while processing the firmware upload.',
      };
    }
  }
}
