import AWS from 'aws-sdk';

/* eslint-disable import/no-internal-modules */
import { UploadCjcFirmwareService } from '../../services/UploadCjcFirmwareService';
/* eslint-enable import/no-internal-modules */
import { UploadCjcFirmwareCriteria } from '../../models/graphQL/UploadCJCFirmwareModel';

jest.mock('aws-sdk', () => {
  const mockS3Instance = {
    headObject: jest.fn().mockImplementation(() => ({
      promise: jest.fn().mockResolvedValue({}),
    })),
    getSignedUrl: jest.fn().mockReturnValue('https://mocked-s3-url'),
  };

  const S3 = jest.fn(() => mockS3Instance);

  return {
    S3,
  };
});

describe('UploadCjcFirmwareService', () => {
  it('should return success and a presigned URL for a valid request with overwrite enabled', async () => {
    const mockS3Instance = new AWS.S3();

    const service = new UploadCjcFirmwareService();

    const criteria: UploadCjcFirmwareCriteria = {
      firmwareName: 'ESP32_filesystem' as any,
      version: '1.0.0',
      targetDirectory: 'bulksAll' as any,
      isOverwrite: true,
      updatedBy: '',
    };

    const authToken = 'mocked-auth-token';

    const result = await service.handleFirmwareUpload(criteria, authToken);

    expect(mockS3Instance.headObject).toHaveBeenCalledWith({
      Bucket: expect.any(String),
      Key: expect.stringContaining('bulksAll/1.0.0/ESP32_filesystem.bin'),
    });

    expect(mockS3Instance.getSignedUrl).toHaveBeenCalledWith('putObject', {
      Bucket: expect.any(String),
      Key: expect.stringContaining('bulksAll/1.0.0/ESP32_filesystem.bin'),
      Expires: expect.any(Number),
      ContentType: expect.any(String),
    });

    expect(result).toEqual({
      success: true,
      message: 'Firmware upload is valid. Use the provided S3 URL to upload the firmware file.',
      uploadUrl: 'https://mocked-s3-url',
    });
  });
});
