import * as SDK from '@carrier-io/backend-lib-node-sdk';
import { BaseAssetEntityClass } from '@carrier-io/io-lib-node-sdk-models';
import { LocationEntity } from './LocationEntity';
import { EdgeEntity } from './EdgeEntity';

@SDK.entityName('asset')
export class AssetEntity extends BaseAssetEntityClass {
  @SDK.entityField(SDK.NodeFieldScope.custom)
  assetId: string;

  @SDK.entityRelation({
    backref: 'assets',
    getTargetNode: () => LocationEntity,
    scope: SDK.NodeFieldScope.custom,
  })
  location?: SDK.RelationToMany<LocationEntity>;

  @SDK.entityRelation({
    backref: 'assets',
    getTargetNode: () => EdgeEntity,
    scope: SDK.NodeFieldScope.common,
  })
  // @ts-ignore
  edges?: SDK.RelationToMany<EdgeEntity>;
}
