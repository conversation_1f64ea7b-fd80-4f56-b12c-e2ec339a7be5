{"app": "yarn build && npx ts-node --project ./tsconfig.build.json --prefer-ts-exts infra/index.ts", "watch": {"include": ["**"], "exclude": ["**/*.d.ts", "**/*.js", "cdk*.json", "node_modules", "package*.json", "test", "tsconfig.json", "yarn.lock"]}, "context": {"@aws-cdk-containers/ecs-service-extensions:enableDefaultLogDriver": true, "@aws-cdk/aws-apigateway:usagePlanKeyOrderInsensitiveId": true, "@aws-cdk/aws-cloudfront:defaultSecurityPolicyTLSv1.2_2021": true, "@aws-cdk/aws-ec2:uniqueImdsv2TemplateName": true, "@aws-cdk/aws-iam:minimizePolicies": true, "@aws-cdk/aws-lambda:recognizeVersionProps": true, "@aws-cdk/core:checkSecretUsage": true, "@aws-cdk/aws-rds:lowercaseDbIdentifier": true, "@aws-cdk/core:stackRelativeExports": true, "@aws-cdk/core:target-partitions": ["aws"]}}