import { type MiddlewareFn } from 'type-graphql';

import { logger } from '../helpers';

export const LoggerGraphQLData: MiddlewareFn = async ({ info, args }, next) => {
  if (info.path.typename === 'Query') {
    logger.info({ message: `New Request to ${info.fieldName}`, args });
  } else if (info.path.typename === 'Mutation') {
    logger.info({ message: `New Mutation ${info.fieldName}`, args });
  }
  const result = await next();
  if (result && (info.path.typename === 'Query' || info.path.typename === 'Mutation')) {
    logger.info({ message: `Successful response`, result: JSON.stringify(result) });
  }
  return result;
};
