import { App } from 'aws-cdk-lib';

export class ConfigVariables {
  static from(app: App) {
    // Get the per-environment staging configuration
    const stageConfig = {
      myConfigValueFromContext: app.node.tryGetContext('stage'),
    };
    return {
      ...stageConfig,
      ...{
        /** ********************************
         * Environment-Agnostic Variables *
         **********************************
         * These are merged with the Environment-Specific variables.
         ********************************* */
      },
    };
  }
}

// vim:expandtab:ft=typescript:sw=4:ts=4
