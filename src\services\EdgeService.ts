import { BaseService, SelectParams } from '@carrier-io/backend-lib-node-sdk';
import { EdgeEntity } from '../models/entities/EdgeEntity';

export const EdgeService = new BaseService<EdgeEntity>(EdgeEntity);

export const getEdgeDataBySerialNumber = async (edgeSerialNumber: string) => {
  const requestParams: SelectParams<EdgeEntity> = {
    filters: {
      serialNumber: { equalTo: edgeSerialNumber },
    },
    include: ['assets'],
  };
  return EdgeService.select(requestParams);
};
