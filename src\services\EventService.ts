import { Model } from 'mongoose';
import { EventInDb, IEventLog } from '../models/interfaces/LogEvent';
import { MongoDBAdapter } from '../adapters';
import { EventModel } from '../schemas/event.schema';
import { EventPayload } from '../models/interfaces/EventPayload';
import { getEdgeDataBySerialNumber } from './EdgeService';

export class EventService {
  public async createNewEvent(eventPayload: EventPayload) {
    const { edgeSerialNumber, userName, eventName, targetUpdateOperation, targetVersions } = eventPayload;
    const edgeDetails = await getEdgeDataBySerialNumber(edgeSerialNumber);

    const event: IEventLog = {
      timestamp: +new Date(),
      edgeId: edgeDetails[0]?.id,
      assetSN: '',
      assetName: '',
      assetId: '',
      name: eventName,
      description: `${userName} updated ${targetVersions
        .map((target) => target.firmwareName)
        .join(', ')} to ${targetVersions.map((target) => target.version).join(', ')} for ${
        edgeDetails[0]?.serialNumber
      }`,
      properties: [
        { key: 'targetDirectory', value: targetUpdateOperation },
        { key: 'targetVersions', value: JSON.stringify(targetVersions) },
      ],
      edgeSN: edgeDetails[0]?.serialNumber,
    };
    const db = new MongoDBAdapter();
    await db.connect();

    const model: Model<EventInDb> = EventModel;
    return model.create(event);
  }
}
