import { IoTClient, DescribeJobCommand } from '@aws-sdk/client-iot';
import { IotService } from '../../services/IotService';
import { StsConfigService } from '../../services/StsConfigService';
import { mockCredentials } from '../mocks/mocks';

jest.mock('@aws-sdk/client-iot', () => ({
  IoTClient: jest.fn().mockImplementation(() => ({
    send: jest.fn(),
  })),
  DescribeJobCommand: jest.fn(),
  JobStatus: {
    COMPLETED: 'COMPLETED',
    IN_PROGESS: 'IN_PROGESS',
  },
}));

jest.mock('../../services/StsConfigService', () => ({
  StsConfigService: jest.fn().mockImplementation(() => ({
    getSTSCredentials: jest.fn().mockResolvedValue({
      accessKeyId: 'mockAccessKey',
      secretAccessKey: 'mockSecretKey',
      sessionToken: 'mockSessionToken',
      region: 'mockRegion',
    }),
  })),
}));

jest.mock('../../helpers', () => ({
  logger: {
    error: jest.fn(),
  },
}));

describe('IotService', () => {
  let service: IotService;
  let mockSend: jest.Mock;
  let mockDescribeJobCommand: jest.Mock;

  beforeEach(() => {
    mockSend = jest.fn();
    mockDescribeJobCommand = jest.fn();
    (IoTClient as jest.Mock).mockImplementation(() => ({
      send: mockSend,
    }));
    (DescribeJobCommand as unknown as jest.Mock).mockImplementation(mockDescribeJobCommand);

    service = IotService.getInstance();
  });

  it('should return the same instance every time', () => {
    const instance1 = IotService.getInstance();
    const instance2 = IotService.getInstance();
    expect(instance1).toBe(instance2);
  });

  it('should send IoT job command successfully', async () => {
    const jobCommand = {};
    await service.sendIotJob(jobCommand);

    expect(mockSend).toHaveBeenCalledTimes(1);
    expect(mockSend).toHaveBeenCalledWith(jobCommand);
  });

  it('should fetch new credentials if not already cached', async () => {
    const stsService = new StsConfigService();
    const credentials = await stsService.getSTSCredentials();

    expect(credentials).toEqual(mockCredentials);
  });

  it('should reuse cached credentials if they are not expired', async () => {
    const stsService = new StsConfigService();
    const credentials1 = await stsService.getSTSCredentials();
    const credentials2 = await stsService.getSTSCredentials();
    expect(credentials1).toBe(credentials2);
  });
});
