import { CjcOtaJobResolver } from '../../resolver/CjcOtaJob.resolver';
import { FirmwareJobService } from '../../services/FirmwareJobService';
import {
  mockJobIds,
  mockDescribeJobServiceResponse,
  mockDescribeJobExecutionServiceResponse,
  mockContext,
} from '../mocks/mocks';
import * as xrayUtil from '../../helpers/x-ray-util';
import { MongoDBAdapter } from '../../adapters/mongodb';

jest.mock('../../services/FirmwareJobService');
jest.mock('../../helpers/x-ray-util');
jest.mock('../../adapters/mongodb');

const MOCK_CONTEXT_HEADERS = 'x-amzn-trace-id';
const MOCK_REQUEST_ID = 'mock-request-id';
const FIRMWARE_JOB_ERROR = 'Firmware Job error';
const FIRMWARE_JOB_EXECUTION_ERROR = 'Firmware Job Execution error';

describe('CjcOtaJobResolver', () => {
  let resolver: any;
  let mockFirmwareJobService: jest.Mocked<FirmwareJobService>;
  let mockMongoDBAdapter: jest.Mocked<MongoDBAdapter>;

  beforeEach(() => {
    mockFirmwareJobService = new FirmwareJobService() as jest.Mocked<FirmwareJobService>;
    mockMongoDBAdapter = new MongoDBAdapter() as jest.Mocked<MongoDBAdapter>;
    mockMongoDBAdapter.connect = jest.fn().mockResolvedValue(undefined);

    resolver = new CjcOtaJobResolver();
    resolver._firmwareJobService = mockFirmwareJobService;
    resolver.mongoDb = mockMongoDBAdapter;
  });

  describe('describeFirmwareJob', () => {
    it('should handle errors from FirmwareJobService', async () => {
      mockFirmwareJobService.describeFirmwareJob.mockRejectedValue(new Error(FIRMWARE_JOB_ERROR));

      const result = await resolver.describeFirmwareJob(mockContext, mockJobIds);
      expect(result).toEqual({ data: [], error: { message: FIRMWARE_JOB_ERROR } });
    });

    it('should describe firmware job for the given jobIds', async () => {
      mockFirmwareJobService.describeFirmwareJob.mockResolvedValue(mockDescribeJobServiceResponse);

      const result = await resolver.describeFirmwareJob(mockContext, mockJobIds);

      expect(mockFirmwareJobService.describeFirmwareJob).toHaveBeenCalledWith(mockJobIds.jobIds);
      expect(result).toEqual({ data: mockDescribeJobServiceResponse });
    });

    it('should initialize xray and handle error', async () => {
      const mockError = new Error(FIRMWARE_JOB_ERROR);
      mockFirmwareJobService.describeFirmwareJob.mockRejectedValue(mockError);

      const mockInitialize = jest.spyOn(xrayUtil, 'initialize').mockReturnValue({
        close: jest.fn(),
      } as any);
      const mockHandleErrors = jest.spyOn(xrayUtil, 'handleErrors').mockImplementation(jest.fn());

      const result = await resolver.describeFirmwareJob(mockContext, mockJobIds);
      expect(result).toEqual({ data: [], error: { message: FIRMWARE_JOB_ERROR } });
      expect(mockInitialize).toHaveBeenCalledWith(
        'DescribeFirmwareJob',
        mockContext.event.headers[MOCK_CONTEXT_HEADERS],
      );
      expect(mockHandleErrors).toHaveBeenCalledWith(mockError, expect.anything(), MOCK_REQUEST_ID);
    });

    it('should handle successful describeFirmwareJob and close xray segment', async () => {
      mockFirmwareJobService.describeFirmwareJob.mockResolvedValue(mockDescribeJobServiceResponse);

      const mockInitialize = jest.spyOn(xrayUtil, 'initialize').mockReturnValue({
        close: jest.fn(),
      } as any);

      await resolver.describeFirmwareJob(mockContext, mockJobIds);

      expect(mockInitialize).toHaveBeenCalledWith(
        'DescribeFirmwareJob',
        mockContext.event.headers[MOCK_CONTEXT_HEADERS],
      );
    });
  });
  describe('describeFirmwareJobExecution', () => {
    it('should handle errors from FirmwareJobService', async () => {
      mockFirmwareJobService.describeFirmwareJobExecution.mockRejectedValue(new Error(FIRMWARE_JOB_EXECUTION_ERROR));

      const result = await resolver.describeFirmwareJobExecution(mockContext, mockJobIds);
      expect(result).toEqual({ data: [], error: { message: FIRMWARE_JOB_EXECUTION_ERROR } });
    });

    it('should describe firmware job execution for the given jobIds', async () => {
      mockFirmwareJobService.describeFirmwareJobExecution.mockResolvedValue(mockDescribeJobExecutionServiceResponse);

      const result = await resolver.describeFirmwareJobExecution(mockContext, mockJobIds);

      expect(mockFirmwareJobService.describeFirmwareJobExecution).toHaveBeenCalledWith(mockJobIds.jobIds);
      expect(result).toEqual({ data: mockDescribeJobExecutionServiceResponse });
    });

    it('should initialize xray and handle errors during describeFirmwareJobExecution', async () => {
      const mockError = new Error(FIRMWARE_JOB_EXECUTION_ERROR);
      mockFirmwareJobService.describeFirmwareJobExecution.mockRejectedValue(mockError);

      const mockInitialize = jest.spyOn(xrayUtil, 'initialize').mockReturnValue({
        close: jest.fn(),
      } as any);
      const mockHandleErrors = jest.spyOn(xrayUtil, 'handleErrors').mockImplementation(jest.fn());

      const result = await resolver.describeFirmwareJobExecution(mockContext, mockJobIds);
      expect(result).toEqual({ data: [], error: { message: FIRMWARE_JOB_EXECUTION_ERROR } });
      expect(mockInitialize).toHaveBeenCalledWith(
        'DescribeFirmwareJobExecution',
        mockContext.event.headers[MOCK_CONTEXT_HEADERS],
      );
      expect(mockHandleErrors).toHaveBeenCalledWith(mockError, expect.anything(), MOCK_REQUEST_ID);
    });

    it('should handle successful describeFirmwareJobExecution and close xray segment', async () => {
      mockFirmwareJobService.describeFirmwareJobExecution.mockResolvedValue(mockDescribeJobExecutionServiceResponse);

      const mockInitialize = jest.spyOn(xrayUtil, 'initialize').mockReturnValue({
        close: jest.fn(),
      } as any);

      await resolver.describeFirmwareJobExecution(mockContext, mockJobIds);

      expect(mockInitialize).toHaveBeenCalledWith(
        'DescribeFirmwareJobExecution',
        mockContext.event.headers[MOCK_CONTEXT_HEADERS],
      );
    });
  });
});
