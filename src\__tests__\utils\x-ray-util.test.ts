import { initialize, handleErrors, getTraceHeader } from '../../helpers/x-ray-util';

jest.mock('aws-xray-sdk-core');
beforeEach(() => {
  process.env._X_AMZN_TRACE_ID = 'Root=1-5759e988-bd862e3fe1be46a994272793;Parent=53995c3f42cd8ad8';
  console.log = jest.fn();
});

afterEach(() => {
  jest.clearAllMocks();
});

describe('X-ray-util::getTraceHeader', () => {
  it('should get header value', () => {
    expect(getTraceHeader()).toBeDefined();
  });

  it('should be initialised', () => {
    const rootSegment = initialize('serviceName');
    expect(rootSegment).toBeDefined();
  });

  it('should be handleErrors', () => {
    const error = new Error('error msg');
    const rootSegment = initialize('serviceName');
    handleErrors(error, rootSegment, '');
  });
});
