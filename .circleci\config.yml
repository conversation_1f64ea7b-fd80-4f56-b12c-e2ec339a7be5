version: 2.1
parameters:
  environment:
    type: string
    default: dev
  main-branch:
    type: string
    default: 'main'
  premerge-build:
    type: boolean
    default: false
  repository-name:
    type: string
    default: ''
  resource-class-post:
    type: string
    default: ""
    description: "Ensure starts with character '.' such as to change the architecture (Examples: '.gen1','.multi')"
  resource-class-pre:
    type: string
    default: ""
    description: "Ensure ends with character '.' such as to change the architecture (Examples: 'arm.', 'macos.x86.')"
  workflow_controller:
    default: false
    type: boolean
  aws_codeartifact_partition:
    type: string
    default: 'aws'
    description: >
      AWS Partition used to login to CodeArtifact
  aws_codeartifact_account_id:
    type: string
    default: '************'  # CarrierIO Prod
    description: >
      AWS Account used to login to CodeArtifact
  aws_codeartifact_region_name:
    type: string
    default: 'us-east-1'
    description: >
      AWS Region used to login to CodeArtifact
  aws_partition:
    type: string
    default: 'aws'
    description: >
      AWS Partition that will be deployed to
  aws_account_id_dev:
    type: string
    default: "************"
    description: >
      Dev AWS Account that will be deployed to 
  aws_account_id_qa:
    type: string
    default: "************"
    description: >
      QA AWS Account that will be deployed to
  aws_account_id_preprod:
    type: string
    default: "************"
    description: >
      PreProd AWS Account that will be deployed to 
  aws_account_id_prod:
    type: string
    default: "************"
    description: >
      Prod AWS Account that will be deployed to
  aws_region_name:
    type: string
    default: 'us-east-1'
    description: >
      AWS Region that will be deployed to
  webhook:
    type: string
    default: 'add a valid teams webhook url'
    description: >
      Setting an invalid value will ensure that no messages are sent and the attempts to send messages fail gracefully
      (without interrupting the remainder of the pipeline)
      Do add a valid teams channel webhook if notification is required.
      environment variable    
orbs:
  aws-auth: cardig/aws-auth@1
  awsauth: cardig/awsauth@2
  aws-cli: circleci/aws-cli@2
  aws-s3: circleci/aws-s3@3
  aws-ssm: cardig/aws-ssm@1
  jira: cardig/priv-jira@3
  priv-aws-auth: cardig/priv-aws-auth@0
  priv-ca: cardig/priv-code-artifact@2
  priv-cache: cardig/priv-cache@1
  priv-code-analysis: cardig/priv-code-analysis@2
  priv-osv-scan: cardig/priv-osv-scan@1
  priv-synopsis: cardig/priv-synopsis@2
  priv-workflow: cardig/priv-workflow@4
  semver: cardig/semver@1
  msteams-notifications: cardig/msteams-notifications@2

executors:
  default-executor: &default-executor
    docker:
      - image: cimg/node:20.10
  
  python-executor:
    docker:
      - image: cimg/python:3.12.2-browsers
  
  frontend-build-executor:
    <<: *default-executor

  scan-executor:
    docker:
      - image: cimg/node:20.11.1-browsers

  sonar-executor:
    <<: *default-executor

  build-executor:
    <<: *default-executor

  osv-executor:
    <<: *default-executor

commands:
  ca-deploy:
    parameters:
      environment:
        type: string
      npm-path:
        type: string
        default: .
    steps:
      - when:
          condition:
            not: <<pipeline.git.tag>>
          steps:
            - priv-ca/ca-deploy-branch:
                npm-path: <<parameters.npm-path>>
                revision: <<pipeline.git.revision>>
                run-publish:
                  - run:
                      command: |
                        cd <<parameters.npm-path>>
                        npm publish
                      name: Publish Package
      - when:
          condition:
            not: <<pipeline.git.branch>>
          steps:
            - priv-ca/ca-deploy-tag:
                environment: <<parameters.environment>>
                npm-path: <<parameters.npm-path>>
                run-publish:
                  - priv-ca/hotfix-publish:
                      npm-path: <<parameters.npm-path>>
                      environment: <<parameters.environment>>

  fail-tag-deployment:
    steps:
      - when:
          condition: << pipeline.git.tag >>
          steps:
            - run:
                command: |
                  new_tag="<< pipeline.git.tag >>-failed"
                  echo "Pushing Tag :     $new_tag"
                  git config --global user.email "<EMAIL>"
                  git config --global user.name "circleci"
                  git tag -a $new_tag $CIRCLE_SHA1 -m 'tag pushed from circleci'
                  git push origin $new_tag
                  echo "Tag pushed!"
                name: Pushed Failed Tag
                when: on_fail
                working_directory: .circleci
            - run:
                command: |
                    echo "Deleting prior tag"
                    git push --delete origin << pipeline.git.tag >>
                name: Delete Failed Trigger Tag
                when: on_fail
                working_directory: .circleci

jobs:
  validate-cdk:
    executor: build-executor
    resource_class: <<pipeline.parameters.resource-class-pre>>large<<pipeline.parameters.resource-class-post>>
    parameters:
      env:
        type: string
        description: |
          Environment name
    steps:
      - checkout
      - priv-workflow/job-prep
      - priv-cache/cached-package-install
      - run: npx cdk synth -c stage=<<parameters.env>>
      - when:
          condition:
            not:
              equal: ['add a valid teams webhook url', <<pipeline.parameters.webhook>>]
          steps:
            - msteams-notifications/status:
                fail_only: true  # Set this to true to test the 'fail-only' logic.
                webhook: <<pipeline.parameters.webhook>>  

  deploy-cdk:
    executor: build-executor
    resource_class: <<pipeline.parameters.resource-class-pre>>large<<pipeline.parameters.resource-class-post>>
    parameters:
      env:
        type: string
        description: |
          Environment name
      cache-token:
        type: string
        default: &cache-token-parameter v1
        description: |
          Parameter used to invalidate a cache
      cdk-output-path:
        type: string
        default: &cdk-output-path /tmp/cdk-outputs.json
        description: |
          Path to the CDK outputs JSON file
    steps:
      - checkout
      - aws-cli/install
      - priv-workflow/job-prep
      - priv-cache/cached-package-install
      - run:
         command: |
          npx cdk deploy \
            --require-approval never \
            -c stage=<<parameters.env>> \
            --outputs-file << parameters.cdk-output-path >> \
            <<parameters.env>>/*
         # Increase Default CircleCI execution time 10m to 20m
         no_output_timeout: 20m
      - save_cache:
          key: cdk-outputs-<< parameters.cache-token >>-<< parameters.env >>-<< pipeline.git.revision >>
          paths:
            - << parameters.cdk-output-path >>
      - when:
          condition:
            not:
              equal: ['add a valid teams webhook url', <<pipeline.parameters.webhook>>]
          steps:
            - msteams-notifications/status:
                fail_only: true  # Set this to true to test the 'fail-only' logic.
                webhook: <<pipeline.parameters.webhook>>        
  ca-prep:
    executor: <<parameters.executor>>
    resource_class: <<pipeline.parameters.resource-class-pre>>large<<pipeline.parameters.resource-class-post>>
    parameters:
      environment:
        type: string
      executor:
        type: executor
        default: build-executor
      npm-path:
        type: string
        default: .
        description: Path to the NPM package
    steps:
      - checkout
      - priv-workflow/job-prep
      - when:
          condition:
            equal: [ <<pipeline.parameters.main-branch>>, <<pipeline.git.branch>> ]
          steps:
            - priv-ca/validate-branch-name
      - when:
          condition:
            and:
              - not: << pipeline.git.tag >>
              - not:
                  equal: [<< pipeline.parameters.main-branch >>, << pipeline.git.branch >>]
          steps:
            - priv-ca/cache-branch-name
      - when:
          condition:
            not: << pipeline.git.branch >>
          steps:
            - priv-ca/prevent-dist-tag-downgrade:
                environment: <<parameters.environment>>
                npm-path: <<parameters.npm-path>>
      - when:
          condition:
            not:
              equal: ['add a valid teams webhook url', <<pipeline.parameters.webhook>>]
          steps:
            - msteams-notifications/status:
                fail_only: true  # Set this to true to test the 'fail-only' logic.
                webhook: <<pipeline.parameters.webhook>>            

  yarn-test:
    executor: <<parameters.executor>>
    parameters:
      env:
        type: string
      executor:
        type: executor
        default: build-executor
    steps:
      - checkout
      - priv-workflow/job-prep
      - priv-cache/cached-package-install
      - run: yarn test:unit
      - when:
          condition:
            not:
              equal: ['add a valid teams webhook url', <<pipeline.parameters.webhook>>]
          steps:
            - msteams-notifications/status:
                fail_only: true  # Set this to true to test the 'fail-only' logic.
                webhook: <<pipeline.parameters.webhook>>  

  deploy-codeartifact:
    executor: <<parameters.executor>>
    resource_class: <<pipeline.parameters.resource-class-pre>>large<<pipeline.parameters.resource-class-post>>
    parameters:
      environment:
        type: string
      executor:
        type: executor
        default: build-executor
      npm-path:
        type: string
        default: .
    steps:
      - checkout
      - priv-workflow/job-prep
      - priv-cache/cached-package-install
      - ca-deploy:
          environment: <<parameters.environment>>
          npm-path: <<parameters.npm-path>>
      - when:
          condition:
            not:
              equal: ['add a valid teams webhook url', <<pipeline.parameters.webhook>>]
          steps:
            - msteams-notifications/status:
                fail_only: true  # Set this to true to test the 'fail-only' logic.
                webhook: <<pipeline.parameters.webhook>>      

workflows:
  # dev deployment
  branch-deployer:
    jobs:
      - priv-workflow/check-pr:
          name: Check PR Status
          filters:
            branches:
              ignore:
                - <<pipeline.parameters.main-branch>>
      - ca-prep: &ca-prep-step
          name: CA Prep
          environment: dev
          pre-steps: &steps-workspace-auth
            - checkout
            - awsauth/oidc-authentication:
                aws-account-id: << pipeline.parameters.aws_codeartifact_account_id >>
                aws-partition: << pipeline.parameters.aws_codeartifact_partition >>
                aws-region-name: << pipeline.parameters.aws_codeartifact_region_name >>
            - priv-aws-auth/ca-login:
                domain: "carrier"
                repository: "@carrier-io"
                domain_owner: "************"
            - priv-aws-auth/ca-login:
                domain: "carrier"
                repository: "@carrier-io-insights"
                domain_owner: "************"
            - priv-aws-auth/ca-login:
                domain: "carrier"
                repository: "@carrier"
                domain_owner: "************"
            - priv-aws-auth/ca-login:
                domain: "carrier"
                repository: "npm-store"
                domain_owner: "************"
            - priv-aws-auth/ca-login:
                domain: "carrier"
                repository: "@zephyr"
                domain_owner: "************"
            - priv-aws-auth/ca-login:
                domain: carrier
                repository: '@abound'
                domain_owner: '************'
            - priv-aws-auth/ca-login:
                domain: iot-carrier
                repository: 'prod.iot'
                domain_owner: '************'
      - yarn-test: &yarn-test-step
          name: Yarn Test
          env: dev
          pre-steps: 
            - *steps-workspace-auth
      - validate-cdk: &validate-cdk-step
          name: Validate CDK - dev
          env: dev
          pre-steps: 
            - *steps-workspace-auth
            - awsauth/oidc-authentication: &aws-oidc-auth-global-dev 
                aws-account-id: << pipeline.parameters.aws_account_id_dev >>
                aws-partition: << pipeline.parameters.aws_partition >>
                aws-region-name: << pipeline.parameters.aws_region_name >>
      - deploy-codeartifact: &deploy-codeartifact-step
          name: Deploy CodeArtifact - dev
          environment: dev
          pre-steps: 
            - *steps-workspace-auth
          filters:
            branches:
              only:
                - <<pipeline.parameters.main-branch>>
          requires:
            - CA Prep
      - deploy-cdk: &deploy-cdk-step
          name: Deploy CDK - dev
          filters:
            branches:
              only:
                - <<pipeline.parameters.main-branch>>
          pre-steps: 
            - *steps-workspace-auth
            - awsauth/oidc-authentication: 
                <<: *aws-oidc-auth-global-dev
          env: dev
          requires:
            - Validate CDK - dev
            - Deploy CodeArtifact - dev
      - jira/get-and-post: &jira-post
          name: Update Jira dev
          deploy-environment: dev
          commit-history-type: branch
          filters:
            branches:
              only:
                - <<pipeline.parameters.main-branch>>
          requires:
            - Deploy CDK - dev
            - Deploy CodeArtifact - dev
    when:
      and:
        - not: << pipeline.git.tag >>
        - not: << pipeline.parameters.premerge-build >>

  premerge-deployer:
    jobs:
      - priv-osv-scan/osv-scan:
          name : OSV Scan
          pre-steps: 
            - *steps-workspace-auth
      - priv-workflow/sync-main:
          name: Merge in <<pipeline.parameters.main-branch>>
          main-branch: <<pipeline.parameters.main-branch>>
          requires:
            - OSV Scan
      - validate-cdk:
          <<: *validate-cdk-step
          requires:
            - Merge in <<pipeline.parameters.main-branch>>
      - yarn-test:
          <<: *yarn-test-step
          requires:
            - Merge in <<pipeline.parameters.main-branch>>
    when:
      and:
        - not: << pipeline.git.tag >>
        - << pipeline.parameters.premerge-build >>

  tag-router:
    jobs:
      - priv-ca/tag-routing: &tag-routing
          filters:
            tags:
              only: /rc/
          name: Handle RC Tag
          environment: qa
          pipeline-number: <<pipeline.number>>
          main-branch: <<pipeline.parameters.main-branch>>
          route: tag
      - priv-ca/tag-routing:
          <<: *tag-routing
          filters:
            tags:
              only: /hotfix-(qa|preprod|prod)/
          name: Handle Hotfix Tag
          environment: <<pipeline.parameters.environment>>
          route: hotfix-tag
      - priv-ca/tag-routing:
          <<: *tag-routing
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-qa\+([0-9]*)\.([a-f0-9]){7}/
          name: Handle QA Tag
          environment: qa
          route: controller
      - priv-ca/tag-routing:
          <<: *tag-routing
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-preprod\+([0-9]*)\.([a-f0-9]){7}/
          name: Handle PreProd Tag
          environment: preprod
          route: controller
      - priv-ca/tag-routing:
          <<: *tag-routing
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-prod\+([0-9]*)\.([a-f0-9]){7}/
          name: Handle Prod Tag
          environment: prod
          route: controller
    when:
      and:
        - << pipeline.git.tag >>
        - not: << pipeline.parameters.workflow_controller >>

  # qa - prod deployment
  tag-deployer:
    jobs:
      - priv-synopsis/blackduck-scan:
          name: Black Duck Scan
          executor: scan-executor
          context:
            - SYNOPSIS_BLACKDUCK
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-qa\+([0-9]*)\.([a-f0-9]){7}/
          scan-prep:
            - priv-cache/cached-package-install
          pre-steps:
            - *steps-workspace-auth
          post-steps:
            - when:
                condition:
                  not:
                    equal: ['add a valid teams webhook url', <<pipeline.parameters.webhook>>]
                steps:
                  - msteams-notifications/status:
                      fail_only: true  # Set this to true to test the 'fail-only' logic.
                      webhook: <<pipeline.parameters.webhook>>    
      
      - priv-synopsis/polaris-scan:
          name: Polaris Scan
          executor: scan-executor
          context:
            - SYNOPSIS_POLARIS
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-qa\+([0-9]*)\.([a-f0-9]){7}/
          scan-prep:
            - priv-cache/cached-package-install
          pre-steps:
            - *steps-workspace-auth
          post-steps:
            - when:
                condition:
                  not:
                    equal: ['add a valid teams webhook url', <<pipeline.parameters.webhook>>]
                steps:
                  - msteams-notifications/status:
                      fail_only: true  # Set this to true to test the 'fail-only' logic.
                      webhook: <<pipeline.parameters.webhook>>    
      
      - ca-prep:
          <<: *ca-prep-step
          environment: <<pipeline.parameters.environment>>
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-<< pipeline.parameters.environment >>.*/
      - deploy-codeartifact:
          <<: *deploy-codeartifact-step
          name: Deploy CodeArtifact - <<pipeline.parameters.environment>>
          environment: <<pipeline.parameters.environment>>
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-<< pipeline.parameters.environment >>.*/
          requires:
            - CA Prep
      - deploy-cdk:
          <<: *deploy-cdk-step
          name: Deploy CDK - <<pipeline.parameters.environment>>
          pre-steps: &steps-workspace-oidc-auth-global-tagged
            - *steps-workspace-auth
            - when:
                condition:
                  equal: [ qa, << pipeline.parameters.environment >> ]
                steps:
                  - awsauth/oidc-authentication: 
                      aws-account-id: << pipeline.parameters.aws_account_id_qa >>
                      aws-partition: << pipeline.parameters.aws_partition >>
                      aws-region-name: << pipeline.parameters.aws_region_name >>
            - when:
                condition:
                  equal: [ preprod, <<pipeline.parameters.environment>> ]
                steps:
                  - awsauth/oidc-authentication: 
                      aws-account-id: << pipeline.parameters.aws_account_id_preprod >>
                      aws-partition: << pipeline.parameters.aws_partition >>
                      aws-region-name: << pipeline.parameters.aws_region_name >>
            - when:
                condition:
                  equal: [ prod, <<pipeline.parameters.environment>> ]
                steps:
                  - awsauth/oidc-authentication: 
                      aws-account-id: << pipeline.parameters.aws_account_id_prod >>
                      aws-partition: << pipeline.parameters.aws_partition >>
                      aws-region-name: << pipeline.parameters.aws_region_name >>
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-<< pipeline.parameters.environment >>.*/
          env: <<pipeline.parameters.environment>>
          requires:
            - Deploy CodeArtifact - <<pipeline.parameters.environment>>
      - jira/get-and-post:
          <<: *jira-post
          name: Update Jira <<pipeline.parameters.environment>>
          commit-history-type: tag
          deploy-environment: <<pipeline.parameters.environment>>
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-<< pipeline.parameters.environment >>.*/
          requires:
            - Deploy CDK - <<pipeline.parameters.environment>>
            - Deploy CodeArtifact - <<pipeline.parameters.environment>>
      - approval:
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-qa\+([0-9]*)\.([a-f0-9]){7}/
          name: Promote to Preprod
          requires:
            - Deploy CDK - <<pipeline.parameters.environment>>
          type: approval
      - priv-ca/tag-routing:
          <<: *tag-routing
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-qa\+([0-9]*)\.([a-f0-9]){7}/
          name: Push Preprod Tag
          environment: preprod
          requires:
              - Promote to Preprod
      - approval:
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-preprod\+([0-9]*)\.([a-f0-9]){7}/
          name: Promote to Production
          requires:
            - Deploy CDK - <<pipeline.parameters.environment>>
          type: approval
      - priv-ca/tag-routing:
          <<: *tag-routing
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-preprod\+([0-9]*)\.([a-f0-9]){7}/
          name: Push Prod Tag
          environment: prod
          requires:
              - Promote to Production
      - priv-ca/tag-routing:
          <<: *tag-routing
          filters:
            tags:
                only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-prod\+([0-9]*)\.([a-f0-9]){7}/
          name: Push Release Tag
          environment: release
          requires:
            - Deploy CDK - <<pipeline.parameters.environment>>
            - Deploy CodeArtifact - <<pipeline.parameters.environment>>
    when:
      and:
        - << pipeline.git.tag >>
        - << pipeline.parameters.workflow_controller >>

# vim:expandtab:ft=yaml:sw=2:ts=2
