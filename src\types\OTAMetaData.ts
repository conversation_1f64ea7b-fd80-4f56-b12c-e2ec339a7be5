import { JobStatus as AwsJobStatus } from '@aws-sdk/client-iot';
import { Connectivity, JobStatus } from '../enum';

interface MappingType {
  [key: string]: string;
}

export const ConnectivityMap: MappingType = {
  [Connectivity.Online]: 'Online',
  [Connectivity.Offline]: 'Offline',
};

export const JobStatusMap: MappingType = {
  [JobStatus.Success]: 'Success',
  [JobStatus.Failed]: 'Failed',
  [JobStatus.InProgress]: 'In Progress',
  [JobStatus.Queued]: 'Queued',
  [JobStatus.Canceled]: 'Canceled',
  [JobStatus.TimedOut]: 'Timed Out',
  [JobStatus.Rejected]: 'Rejected',
  [JobStatus.Removed]: 'Removed',
};

export const awsToCustomJobStatusMap: Partial<Record<AwsJobStatus, JobStatus>> = {
  [AwsJobStatus.SCHEDULED]: JobStatus.Queued,
  [AwsJobStatus.IN_PROGRESS]: JobStatus.InProgress,
  [AwsJobStatus.COMPLETED]: JobStatus.Success,
  [AwsJobStatus.CANCELED]: JobStatus.Canceled,
  [AwsJobStatus.DELETION_IN_PROGRESS]: JobStatus.InProgress,
};
