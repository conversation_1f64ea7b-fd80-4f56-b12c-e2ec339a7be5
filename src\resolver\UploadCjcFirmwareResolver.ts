import * as GQL from 'type-graphql';
import { Ctx } from 'type-graphql';

import { UploadCjcFirmwareCriteria, UploadCjcFirmwareResponse } from '../models/graphQL/UploadCJCFirmwareModel';
/* eslint-disable import/no-internal-modules */
import { UploadCjcFirmwareService } from '../services/UploadCjcFirmwareService';
import { logger } from '../helpers/logger';
/* eslint-enable import/no-internal-modules */

@GQL.Resolver()
export class UploadCjcFirmwareResolver {
  private firmwareUploadService: UploadCjcFirmwareService;

  constructor() {
    this.firmwareUploadService = new UploadCjcFirmwareService();
  }

  @GQL.Mutation(() => UploadCjcFirmwareResponse)
  async uploadCjcFirmware(
    @Ctx() ctx: any,
    @GQL.Arg('criteria', () => UploadCjcFirmwareCriteria, { nullable: false })
    criteria: UploadCjcFirmwareCriteria,
  ): Promise<UploadCjcFirmwareResponse> {
    try {
      return await this.firmwareUploadService.handleFirmwareUpload(criteria, ctx?.event?.headers?.authorization);
    } catch (error) {
      logger.error(`Error occured in uploadCjcFirmware resolver:: ${error}`);
      return {
        success: false,
        message: 'An error occurred while processing the firmware upload.',
      };
    }
  }
}
