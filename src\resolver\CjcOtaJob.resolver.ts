import 'reflect-metadata';
import { <PERSON>solver, Mutation, Arg, Query, Ctx } from 'type-graphql';
import * as AWSXRay from 'aws-xray-sdk-core';
import jwt from 'jsonwebtoken';

import {
  CreateFirmWareJobOutput,
  CreateFirmWareJobInput,
  CreateAHPFirmWareJobInput,
  FirmwareJobResponse,
  DescribeFirmwareJobInput,
  FirmwareJobExecutionResponse,
} from '../models';
import { FirmwareJobService } from '../services/FirmwareJobService';
import { createCreateJobErrorMessage } from '../helpers/utilFunction';
import * as xray from '../helpers/x-ray-util';
import { UserAuthJwt } from '../types/userAuthJwt';
import { AhpJobService } from '../services/AhpJobService';
import { MongoDBAdapter } from '../adapters/mongodb';

const AWS_TRACE_ID_CONSTANT = 'x-amzn-trace-id';

@Resolver()
export class CjcOtaJobResolver {
  private readonly _firmwareJobService: FirmwareJobService;

  private readonly mongoDb: MongoDBAdapter;

  private readonly _ahpJobService: AhpJobService;

  constructor() {
    this._firmwareJobService = new FirmwareJobService();
    this._ahpJobService = new AhpJobService();
    this.mongoDb = new MongoDBAdapter();
  }

  private async init() {
    await this.mongoDb.connect();
  }

  // eslint-disable-next-line class-methods-use-this
  @Mutation(() => CreateFirmWareJobOutput)
  async createFirmwareJob(
    @Ctx() ctx: any,
    @Arg('criteria', () => CreateFirmWareJobInput)
    criteria: CreateFirmWareJobInput,
  ) {
    try {
      await this.init();
      const response: any = {};
      const decodedToken = jwt.decode(ctx?.event?.headers?.authorization) as UserAuthJwt;
      const userName = `${decodedToken?.firstName} ${decodedToken?.lastName}`;
      const { targetUpdateOperation, targetThings, lteModuleUrl = '', targetVersions } = criteria;

      const {
        failedJobIdForThings = [],
        successfullJobIds = [],
        invalidThingsId = [],
      } = await this._firmwareJobService.createOtaJobs(
        targetThings,
        targetVersions,
        targetUpdateOperation,
        userName,
        lteModuleUrl,
      );

      const errorMessage = createCreateJobErrorMessage(invalidThingsId, failedJobIdForThings);
      if (errorMessage) {
        response.error = {
          message: errorMessage,
        };
      }
      response.data = {
        jobIds: successfullJobIds,
      };
      return response;
    } catch (err: any) {
      return {
        error: {
          message: err.message,
        },
      };
    }
  }

  @Query(() => FirmwareJobResponse)
  async describeFirmwareJob(
    @Ctx() ctx: any,
    @Arg('describeFirmwareJobInput') describeFirmwareJobInput: DescribeFirmwareJobInput,
  ): Promise<FirmwareJobResponse> {
    const rootSegment: AWSXRay.Segment = xray.initialize(
      'DescribeFirmwareJob',
      ctx?.event?.headers?.[AWS_TRACE_ID_CONSTANT],
    );
    try {
      await this.init();
      const response = await this._firmwareJobService.describeFirmwareJob(describeFirmwareJobInput.jobIds);
      return {
        data: response,
      };
    } catch (error: any) {
      xray.handleErrors(error, rootSegment, ctx?.context?.awsRequestId);
      return {
        data: [],
        error: {
          message: error?.message,
        },
      };
    } finally {
      if (rootSegment) rootSegment.close();
    }
  }

  @Query(() => FirmwareJobExecutionResponse)
  async describeFirmwareJobExecution(
    @Ctx() ctx: any,
    @Arg('describeFirmwareJobExecutionInput') describeFirmwareJobExecutionInput: DescribeFirmwareJobInput,
  ): Promise<FirmwareJobExecutionResponse> {
    const rootSegment: AWSXRay.Segment = xray.initialize(
      'DescribeFirmwareJobExecution',
      ctx?.event?.headers?.[AWS_TRACE_ID_CONSTANT],
    );
    try {
      const response = await this._firmwareJobService.describeFirmwareJobExecution(
        describeFirmwareJobExecutionInput.jobIds,
      );
      return {
        data: response,
      };
    } catch (error: any) {
      xray.handleErrors(error, rootSegment, ctx?.context?.awsRequestId);
      return {
        data: [],
        error: {
          message: error?.message,
        },
      };
    } finally {
      if (rootSegment) rootSegment.close();
    }
  }

  // AHP FBOX Firmware Update Mutation
  @Mutation(() => CreateFirmWareJobOutput)
  async createAHPFirmwareUpdateJob(
    @Ctx() ctx: any,
    @Arg('criteria', () => CreateAHPFirmWareJobInput)
    criteria: CreateAHPFirmWareJobInput,
  ) {
    try {
      const response: any = {};
      const decodedToken = jwt.decode(ctx?.event?.headers?.authorization) as UserAuthJwt;
      const userName = `${decodedToken?.firstName} ${decodedToken?.lastName}` || '';
      const { targetThings, targetVersions } = criteria;

      const { successfulJobIds, failedJobIdForThings, invalidThingsId } = await this._ahpJobService.createAhpOtaJobs(
        targetThings,
        targetVersions,
        userName,
      );

      const errorMessage = createCreateJobErrorMessage(invalidThingsId as never[], failedJobIdForThings as never[]);
      if (errorMessage) {
        response.error = {
          message: errorMessage,
        };
      }
      response.data = {
        jobIds: successfulJobIds,
      };
      return response;
    } catch (err: any) {
      return {
        error: {
          message: err.message,
        },
      };
    }
  }
}
