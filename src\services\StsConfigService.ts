import { AssumeRoleCommand, STSClient } from '@aws-sdk/client-sts';

import { Credentials } from '../models/interfaces';
import { logger } from '../helpers/logger';

let assumeCredentials: Credentials | null = null;
const roleArn = process.env.CARRIERIO_ROLE_ARN ?? 'arn:aws:iam::550762557643:role/ZephyrPlatformRole-dev';
const region = process.env.AWS_REGION ?? 'us-east-1';
const stageName = (process.env.STAGE as string) ?? 'dev';

export class StsConfigService {
  private readonly sts: STSClient;

  constructor() {
    this.sts = new STSClient({ region });
  }

  async createSTSCredentials(stage: string): Promise<Credentials | undefined> {
    try {
      const assumeRoleCommand = new AssumeRoleCommand({
        RoleArn: roleArn,
        RoleSessionName: `ZephyrPlatformRole-${stage}`,
        DurationSeconds: 3600,
      });

      const data = await this.sts.send(assumeRoleCommand);
      if (data.Credentials) {
        assumeCredentials = {
          accessKeyId: data.Credentials.AccessKeyId!,
          secretAccessKey: data.Credentials.SecretAccessKey!,
          sessionToken: data.Credentials.SessionToken!,
          expiration: new Date(data.Credentials.Expiration!),
          region,
        };
        return assumeCredentials;
      }
      throw new Error('Failed to assume role');
    } catch (error: any) {
      logger.error('Error assuming role', { error });
      throw new Error('Failed to assume role', error);
    }
  }

  public async getSTSCredentials(): Promise<Credentials | undefined> {
    if (!assumeCredentials) {
      logger.info('No cached credentials found, fetching new ones...');
      return this.createSTSCredentials(stageName);
    }
    if (assumeCredentials.expiration && assumeCredentials.expiration.getTime() - Date.now() < 300000) {
      logger.info('Credentials are expired. Fetch new credentials.');
      return this.createSTSCredentials(stageName);
    }
    return assumeCredentials;
  }
}
