import 'reflect-metadata';
import { SQSEvent } from 'aws-lambda';
import { logger } from '../helpers';
import { IotService } from '../services/IotService';
import { EdgeSyncService } from '../services/EdgeSyncService';
import { CurrentJob } from '../models/interfaces/EdgeSyncInterface';
import { MongoDBAdapter } from '../adapters';

export const handler = async (event: SQSEvent) => {
  logger.debug('Event received in Lambda', JSON.stringify(event, null, 2));
  const iotService = new IotService();
  const edgeSyncService = new EdgeSyncService();
  await new MongoDBAdapter().connect();
  for (const record of event.Records) {
    try {
      const bodyDecoded = Buffer.from(record.body, 'base64').toString('utf-8');
      const messageBody = JSON.parse(bodyDecoded);
      const { jobId, status, timestamp, thingArn } = messageBody;
      logger.info('after parsing the mqttpayload recived on topic', JSON.stringify(messageBody, null, 2));
      const thingName = thingArn.split('/')?.[1];
      logger.debug('Thing name', thingName);
      if (!thingName.includes('cjc')) {
        logger.warn(`Skippng.... Non CJC thing found. Current only CJC thing event are processed.`);
        return;
      }
      const gwId = thingName.replace(/^cjc-/, '');
      // eslint-disable-next-line no-await-in-loop
      const targetVersions = await iotService.getJobStatusAndTargetVersions(jobId);
      if (!targetVersions) {
        logger.error(
          `Invalid target Version found, skipping update. targetVersions::: ${JSON.stringify(targetVersions)}`,
        );
      }
      const currentJobDetails: CurrentJob = {
        jobId,
        jobStatus: status || null,
        completedAt: timestamp,
        ...(targetVersions && { targets: targetVersions }),
      };
      // eslint-disable-next-line no-await-in-loop
      await edgeSyncService.updateJobStatusAndVersion(gwId, currentJobDetails);
      logger.debug(`Updated job ${jobId} for thing ${thingName} with status ${status} and traget version`);
    } catch (err) {
      logger.error('Error while updating the job status', `${err}`);
    }
  }
};
