service: localstack

useDotenv: true

plugins:
  - serverless-offline
  - serverless-dotenv-plugin

custom:
  serverless-offline:
    httpPort: 4000
    noTimeout: true
    printOutput: true
  # Optional: for LocalStack integration
  localstack:
    stages: [local]
    host: http://localhost
    edgePort: 4566
    autostart: true
    lambda:
      mountCode: true

provider:
  name: aws
  runtime: nodejs18.x
  region: us-east-1
  versionFunctions: false

functions:
  graphql:
    handler: dist/src/index.default
    events:
      - http:
          path: graphql
          method: any
          cors: true

  sqsHandler:
    handler: dist/src/index.gatewayAcknowledgementHandler
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - MyQueue
              - Arn

resources:
  Resources:
    MyQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: my-test-queue
